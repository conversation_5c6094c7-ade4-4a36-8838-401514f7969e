# Silicon Labs Project Configuration Tools: slcp, v0, Component selection file.
project_name: bt_soc_empty_6
label: bt_soc_empty_6
description: |
  A minimal project structure, that serves as a starting point for custom Bluetooth applications. The application starts advertising after boot and restarts advertising after a connection is closed.
category: Bluetooth Examples
filter:
- name: Device Type
  value: [SoC]
- name: Project Difficulty
  value: [Beginner]
- name: Wireless Technology
  value: [Bluetooth]
package: Bluetooth
quality: production
readme:
- {path: readme.md}
- {path: readme.md}
source:
- {path: app.c}
tag: ['hardware:rf:band:2400']
include:
- path: .
  file_list:
  - {path: app.h}
sdk: {id: simplicity_sdk, version: 2025.6.1}
toolchain_settings: []
component:
- {id: EFR32BG21A020F768IM32}
- {id: app_assert}
- {id: app_os_helper}
- {id: bluetooth_feature_connection_role_central}
- {id: bluetooth_feature_connection_role_peripheral}
- {id: bluetooth_feature_gatt}
- {id: bluetooth_feature_gatt_server}
- {id: bluetooth_feature_legacy_advertiser}
- {id: bluetooth_feature_legacy_scanner}
- {id: bluetooth_feature_sm}
- {id: bluetooth_feature_system}
- {id: bluetooth_stack}
- {id: clock_manager}
- {id: device_init}
- {id: gatt_configuration}
- {id: gatt_service_device_information_override}
- {id: in_place_ota_dfu}
- {id: mpu}
- {id: rail_util_pti}
- {id: sl_main}
other_file:
- {path: image/readme_img0.png}
- {path: image/readme_img1.png}
- {path: image/readme_img2.png}
- {path: image/readme_img3.png}
- {path: image/readme_img4.png}
configuration:
- {name: SL_STACK_SIZE, value: '2752'}
- condition: [psa_crypto]
  name: SL_PSA_KEY_USER_SLOT_COUNT
  value: '0'
ui_hints:
  highlight:
  - {path: config/btconf/gatt_configuration.btconf}
  - {path: readme.md, focus: true}
post_build:
- {path: bt_soc_empty_6.slpb, profile: bt_common}

