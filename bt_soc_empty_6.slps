<?xml version="1.0" encoding="ASCII"?>
<model:MDescriptors xmlns:model="http://www.silabs.com/ss/Studio.ecore">
  <descriptors name="bt_soc_empty_6">
    <properties key="projectCommon.sdkId" value="com.silabs.sdk.stack.sisdk:2025.6.1._309472562"/>
    <properties key="universalConfig.relativeWorkspacePath" value="..\bt_soc_empty_6.slcw"/>
    <properties key="universalConfig.generationDirectory" value=""/>
    <properties key="universalConfig.toolchainCompatibility" value="iar gcc"/>
    <properties key="projectCommon.overwriteCopiedSources" value="false"/>
    <properties key="projectCommon.boardIds" value="com.silabs.board.none:0.0.0"/>
    <properties key="projectCommon.kitId" value=""/>
    <properties key="projectCommon.partId" value="mcu.arm.efr32.bg21.efr32bg21a020f768im32"/>
    <properties key="projectCommon.toolchainId" value="com.silabs.ss.tool.ide.arm.toolchain.gnu.cdt:12.2.1.20221205"/>
    <properties key="universalConfig.projectTypeIds" value="com.silabs.ss.framework.ide.project.core.cdtMbsProjectType"/>
    <properties key="projectCommon.importModeId" value="COPY"/>
    <properties key="universalConfig.compSelProjInstanceId" value="1756258953430"/>
  </descriptors>
</model:MDescriptors>
