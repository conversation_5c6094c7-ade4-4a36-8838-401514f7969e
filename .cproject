<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<?fileVersion 4.0.0?><cproject storage_type_id="org.eclipse.cdt.core.XmlProjectDescriptionStorage">
	<storageModule moduleId="org.eclipse.cdt.core.settings">
		<cconfiguration id="com.silabs.ss.framework.project.toolchain.core.default#com.silabs.ss.tool.ide.arm.toolchain.gnu.cdt:12.2.1.20221205">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="com.silabs.ss.framework.project.toolchain.core.default#com.silabs.ss.tool.ide.arm.toolchain.gnu.cdt:12.2.1.20221205" moduleId="org.eclipse.cdt.core.settings" name="GNU ARM v12.2.1 - Default">
				<macros>
					<stringMacro name="StudioSdkPath" type="VALUE_PATH_DIR" value="${StudioSdkPathFromID:com.silabs.sdk.stack.sisdk:2025.6.1._309472562}"/>
					<stringMacro name="StudioToolchainPath" type="VALUE_PATH_DIR" value="${StudioToolchainPathFromID:com.silabs.ss.tool.ide.arm.toolchain.gnu.cdt:12.2.1.20221205}"/>
				</macros>
				<externalSettings/>
				<extensions>
					<extension id="com.silabs.ss.framework.debugger.core.HEX" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="com.silabs.ss.framework.debugger.core.EBL" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="com.silabs.ss.framework.debugger.core.GBL" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="com.silabs.ss.framework.debugger.core.BIN" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="com.silabs.ss.framework.debugger.core.S37" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GNU_ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule buildConfig.needsApplyStock="true" buildConfig.stockConfigId="com.silabs.ss.framework.project.toolchain.core.default#com.silabs.ss.tool.ide.arm.toolchain.gnu.cdt:12.2.1.20221205" cppBuildConfig.projectBuiltInState="[{&quot;builtinMacrosMap&quot;:{&quot;EFR32BG21A020F768IM32&quot;:&quot;1&quot;,&quot;SL_RAIL_UTIL_PA_CONFIG_HEADER&quot;:&quot;&lt;sl_rail_util_pa_config.h&gt;&quot;,&quot;SL_RAIL_3_API&quot;:&quot;1&quot;,&quot;SL_CODE_COMPONENT_SLEEPTIMER&quot;:&quot;sleeptimer&quot;,&quot;SL_CODE_COMPONENT_POWER_MANAGER&quot;:&quot;power_manager&quot;,&quot;SL_CODE_COMPONENT_HAL_GPIO&quot;:&quot;hal_gpio&quot;,&quot;SLI_RADIOAES_REQUIRES_MASKING&quot;:&quot;1&quot;,&quot;SL_CODE_COMPONENT_CORE&quot;:&quot;core&quot;,&quot;SL_CODE_COMPONENT_SYSTEM&quot;:&quot;system&quot;,&quot;SL_CODE_COMPONENT_HAL_COMMON&quot;:&quot;hal_common&quot;,&quot;SL_COMPONENT_CATALOG_PRESENT&quot;:&quot;1&quot;,&quot;SL_CODE_COMPONENT_SLI_CRYPTO&quot;:&quot;sli_crypto&quot;,&quot;SL_CODE_COMPONENT_DEVICE_PERIPHERAL&quot;:&quot;device_peripheral&quot;,&quot;SL_CODE_COMPONENT_INTERRUPT_MANAGER&quot;:&quot;interrupt_manager&quot;,&quot;SL_CODE_COMPONENT_GPIO&quot;:&quot;gpio&quot;,&quot;CMSIS_NVIC_VIRTUAL&quot;:&quot;1&quot;,&quot;SL_RAIL_LIB_MULTIPROTOCOL_SUPPORT&quot;:&quot;0&quot;,&quot;SL_CODE_COMPONENT_PSEC_OSAL&quot;:&quot;psec_osal&quot;,&quot;CMSIS_NVIC_VIRTUAL_HEADER_FILE&quot;:&quot;\&quot;cmsis_nvic_virtual.h\&quot;&quot;,&quot;MBEDTLS_PSA_CRYPTO_CONFIG_FILE&quot;:&quot;&lt;psa_crypto_config.h&gt;&quot;,&quot;SL_CODE_COMPONENT_SLI_PROTOCOL_CRYPTO&quot;:&quot;sli_protocol_crypto&quot;,&quot;SL_CODE_COMPONENT_SE_MANAGER&quot;:&quot;se_manager&quot;,&quot;BOOTLOADER_APPLOADER&quot;:&quot;1&quot;,&quot;SL_CODE_COMPONENT_CLOCK_MANAGER&quot;:&quot;clock_manager&quot;,&quot;MBEDTLS_CONFIG_FILE&quot;:&quot;&lt;sl_mbedtls_config.h&gt;&quot;,&quot;SL_APP_PROPERTIES&quot;:&quot;1&quot;},&quot;builtinLibraryPathsStr&quot;:&quot;&quot;,&quot;builtinLibraryFilesStr&quot;:&quot;&quot;,&quot;builtinLibraryNames&quot;:[&quot;gcc&quot;,&quot;c&quot;,&quot;m&quot;,&quot;nosys&quot;],&quot;builtinLibraryObjectsStr&quot;:&quot;&quot;,&quot;id&quot;:&quot;&quot;,&quot;builtinIncludesStr&quot;:&quot;studio:/project/config/ studio:/project/config/btconf/ studio:/project/autogen/ studio:/project/ studio:/project/simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/ studio:/project/simplicity_sdk_2025.6.1/app/common/util/app_assert/ studio:/project/simplicity_sdk_2025.6.1/app/common/util/app_timer/ studio:/project/simplicity_sdk_2025.6.1/app/common/util/app_timer/bm/ studio:/project/simplicity_sdk_2025.6.1/protocol/bluetooth/inc/ studio:/project/simplicity_sdk_2025.6.1/platform/common/inc/ studio:/project/simplicity_sdk_2025.6.1/protocol/bluetooth/bgcommon/inc/ studio:/project/simplicity_sdk_2025.6.1/protocol/bluetooth/bgstack/ll/inc/ studio:/project/simplicity_sdk_2025.6.1/platform/bootloader/ studio:/project/simplicity_sdk_2025.6.1/platform/bootloader/api/ studio:/project/simplicity_sdk_2025.6.1/platform/bootloader/core/flash/ studio:/project/simplicity_sdk_2025.6.1/platform/service/clock_manager/inc/ studio:/project/simplicity_sdk_2025.6.1/platform/service/clock_manager/src/ studio:/project/simplicity_sdk_2025.6.1/platform/CMSIS/Core/Include/ studio:/project/simplicity_sdk_2025.6.1/platform/service/device_manager/inc/ studio:/project/simplicity_sdk_2025.6.1/platform/emdrv/common/inc/ studio:/project/simplicity_sdk_2025.6.1/platform/emlib/inc/ studio:/project/simplicity_sdk_2025.6.1/app/bluetooth/common/gatt_service_device_information_override/ studio:/project/simplicity_sdk_2025.6.1/platform/driver/gpio/inc/ studio:/project/simplicity_sdk_2025.6.1/platform/peripheral/inc/ studio:/project/simplicity_sdk_2025.6.1/app/bluetooth/common/in_place_ota_dfu/ studio:/project/simplicity_sdk_2025.6.1/platform/service/interrupt_manager/inc/ studio:/project/simplicity_sdk_2025.6.1/platform/service/interrupt_manager/src/ studio:/project/simplicity_sdk_2025.6.1/platform/service/interrupt_manager/inc/arm/ studio:/project/simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/config/ studio:/project/simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/config/preset/ studio:/project/simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/inc/ studio:/project/simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/ studio:/project/simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/ studio:/project/simplicity_sdk_2025.6.1/platform/service/memory_manager/inc/ studio:/project/simplicity_sdk_2025.6.1/platform/service/memory_manager/src/ studio:/project/simplicity_sdk_2025.6.1/platform/service/memory_manager/profiler/inc/ studio:/project/simplicity_sdk_2025.6.1/platform/service/mpu/inc/ studio:/project/simplicity_sdk_2025.6.1/platform/emdrv/nvm3/inc/ studio:/project/simplicity_sdk_2025.6.1/platform/emdrv/nvm3/config/ studio:/project/simplicity_sdk_2025.6.1/platform/service/power_manager/inc/ studio:/project/simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/inc/ studio:/project/simplicity_sdk_2025.6.1/platform/radio/rail_lib/common/ studio:/project/simplicity_sdk_2025.6.1/platform/radio/rail_lib/protocol/ble/ studio:/project/simplicity_sdk_2025.6.1/platform/radio/rail_lib/protocol/ieee802154/ studio:/project/simplicity_sdk_2025.6.1/platform/radio/rail_lib/protocol/wmbus/ studio:/project/simplicity_sdk_2025.6.1/platform/radio/rail_lib/protocol/zwave/ studio:/project/simplicity_sdk_2025.6.1/platform/radio/rail_lib/chip/efr32/efr32xg2x/ studio:/project/simplicity_sdk_2025.6.1/platform/radio/rail_lib/protocol/sidewalk/ studio:/project/simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/pa-conversions/ studio:/project/simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/pa-conversions/efr32xg21/ studio:/project/simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/rail_util_power_manager_init/ studio:/project/simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/rail_util_pti/ studio:/project/simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/rail_util_rf_path/ studio:/project/simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/inc/ studio:/project/simplicity_sdk_2025.6.1/platform/service/sl_main/inc/ studio:/project/simplicity_sdk_2025.6.1/platform/service/sl_main/src/ studio:/project/simplicity_sdk_2025.6.1/platform/service/sleeptimer/inc/ studio:/project/simplicity_sdk_2025.6.1/platform/security/sl_component/sli_crypto/inc/ studio:/project/simplicity_sdk_2025.6.1/platform/security/sl_component/sl_protocol_crypto/src/ studio:/project/simplicity_sdk_2025.6.1/platform/security/sl_component/sli_psec_osal/inc/ studio:/project/simplicity_sdk_2025.6.1/platform/service/udelay/inc/ studio:/project/config/ studio:/project/config/btconf/ studio:/project/autogen/ studio:/project/ studio:/project/simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/ studio:/project/simplicity_sdk_2025.6.1/app/common/util/app_assert/ studio:/project/simplicity_sdk_2025.6.1/app/common/util/app_timer/ studio:/project/simplicity_sdk_2025.6.1/app/common/util/app_timer/bm/ studio:/project/simplicity_sdk_2025.6.1/protocol/bluetooth/inc/ studio:/project/simplicity_sdk_2025.6.1/platform/common/inc/ studio:/project/simplicity_sdk_2025.6.1/protocol/bluetooth/bgcommon/inc/ studio:/project/simplicity_sdk_2025.6.1/protocol/bluetooth/bgstack/ll/inc/ studio:/project/simplicity_sdk_2025.6.1/platform/bootloader/ studio:/project/simplicity_sdk_2025.6.1/platform/bootloader/api/ studio:/project/simplicity_sdk_2025.6.1/platform/bootloader/core/flash/ studio:/project/simplicity_sdk_2025.6.1/platform/service/clock_manager/inc/ studio:/project/simplicity_sdk_2025.6.1/platform/service/clock_manager/src/ studio:/project/simplicity_sdk_2025.6.1/platform/CMSIS/Core/Include/ studio:/project/simplicity_sdk_2025.6.1/platform/service/device_manager/inc/ studio:/project/simplicity_sdk_2025.6.1/platform/emdrv/common/inc/ studio:/project/simplicity_sdk_2025.6.1/platform/emlib/inc/ studio:/project/simplicity_sdk_2025.6.1/app/bluetooth/common/gatt_service_device_information_override/ studio:/project/simplicity_sdk_2025.6.1/platform/driver/gpio/inc/ studio:/project/simplicity_sdk_2025.6.1/platform/peripheral/inc/ studio:/project/simplicity_sdk_2025.6.1/app/bluetooth/common/in_place_ota_dfu/ studio:/project/simplicity_sdk_2025.6.1/platform/service/interrupt_manager/inc/ studio:/project/simplicity_sdk_2025.6.1/platform/service/interrupt_manager/src/ studio:/project/simplicity_sdk_2025.6.1/platform/service/interrupt_manager/inc/arm/ studio:/project/simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/config/ studio:/project/simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/config/preset/ studio:/project/simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/inc/ studio:/project/simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/ studio:/project/simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/ studio:/project/simplicity_sdk_2025.6.1/platform/service/memory_manager/inc/ studio:/project/simplicity_sdk_2025.6.1/platform/service/memory_manager/src/ studio:/project/simplicity_sdk_2025.6.1/platform/service/memory_manager/profiler/inc/ studio:/project/simplicity_sdk_2025.6.1/platform/service/mpu/inc/ studio:/project/simplicity_sdk_2025.6.1/platform/emdrv/nvm3/inc/ studio:/project/simplicity_sdk_2025.6.1/platform/emdrv/nvm3/config/ studio:/project/simplicity_sdk_2025.6.1/platform/service/power_manager/inc/ studio:/project/simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/inc/ studio:/project/simplicity_sdk_2025.6.1/platform/radio/rail_lib/common/ studio:/project/simplicity_sdk_2025.6.1/platform/radio/rail_lib/protocol/ble/ studio:/project/simplicity_sdk_2025.6.1/platform/radio/rail_lib/protocol/ieee802154/ studio:/project/simplicity_sdk_2025.6.1/platform/radio/rail_lib/protocol/wmbus/ studio:/project/simplicity_sdk_2025.6.1/platform/radio/rail_lib/protocol/zwave/ studio:/project/simplicity_sdk_2025.6.1/platform/radio/rail_lib/chip/efr32/efr32xg2x/ studio:/project/simplicity_sdk_2025.6.1/platform/radio/rail_lib/protocol/sidewalk/ studio:/project/simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/pa-conversions/ studio:/project/simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/pa-conversions/efr32xg21/ studio:/project/simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/rail_util_power_manager_init/ studio:/project/simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/rail_util_pti/ studio:/project/simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/rail_util_rf_path/ studio:/project/simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/inc/ studio:/project/simplicity_sdk_2025.6.1/platform/service/sl_main/inc/ studio:/project/simplicity_sdk_2025.6.1/platform/service/sl_main/src/ studio:/project/simplicity_sdk_2025.6.1/platform/service/sleeptimer/inc/ studio:/project/simplicity_sdk_2025.6.1/platform/security/sl_component/sli_crypto/inc/ studio:/project/simplicity_sdk_2025.6.1/platform/security/sl_component/sl_protocol_crypto/src/ studio:/project/simplicity_sdk_2025.6.1/platform/security/sl_component/sli_psec_osal/inc/ studio:/project/simplicity_sdk_2025.6.1/platform/service/udelay/inc/&quot;,&quot;resolvedOptionsStr&quot;:&quot;[{\&quot;listValues\&quot;:[],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.base\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;gnu.c.compiler.option.warnings.pedantic\&quot;,\&quot;value\&quot;:\&quot;false\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;listValues\&quot;:[],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.base\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.clibs\&quot;,\&quot;value\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.nanospec\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;listValues\&quot;:[],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.base\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.map\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;listValues\&quot;:[],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.base\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.floatingpoint.enable\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;listValues\&quot;:[],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.toolchain.exe\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.toolchain.debug.level\&quot;,\&quot;value\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.toolchain.debug.level.default\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;listValues\&quot;:[],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;gnu.cpp.compiler.option.warnings.pedantic\&quot;,\&quot;value\&quot;:\&quot;false\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;listValues\&quot;:[\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/bgstack/release/libbondingdb.a}\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/connection_subrating/release/libble_host_connection_subrating_stub.a}\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/bgapi_protocol/api3/release/libbgapi_core.a}\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/radio/rail_lib/autogen/librail_release/librail_efr32xg21_gcc_release.a}\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/ble_bgapi/release/libble_bgapi_gatt_client.a}\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/accept_list/release/libble_host_accept_list_stub.a}\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/hci/release/libble_host_hci.a}\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/core/release/libble_host_core.a}\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/hal/release/libble_host_hal_series2.a}\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/bgstack/release/libble_host.a}\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/ble_bgapi/release/libble_bgapi.a}\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/system/release/libble_host_system.a}\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/bgstack/ll/build/gcc/xg21/release/liblinklayer.a}\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/ble_system/release/libble_system.a}\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/bgcommon/lib/build/gcc/cortex-m33/bgcommon/release/libbgcommon.a}\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/ble_bgapi/release/libble_bgapi_gatt_server.a}\&quot;],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.base\&quot;,\&quot;builtin\&quot;:false,\&quot;optionId\&quot;:\&quot;gnu.c.link.option.userobjs\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;listValuesMap\&quot;:{\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/bgstack/release/libbondingdb.a}\&quot;:\&quot;TRUE\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/connection_subrating/release/libble_host_connection_subrating_stub.a}\&quot;:\&quot;TRUE\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/bgapi_protocol/api3/release/libbgapi_core.a}\&quot;:\&quot;TRUE\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/radio/rail_lib/autogen/librail_release/librail_efr32xg21_gcc_release.a}\&quot;:\&quot;TRUE\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/ble_bgapi/release/libble_bgapi_gatt_client.a}\&quot;:\&quot;TRUE\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/accept_list/release/libble_host_accept_list_stub.a}\&quot;:\&quot;TRUE\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/hci/release/libble_host_hci.a}\&quot;:\&quot;TRUE\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/core/release/libble_host_core.a}\&quot;:\&quot;TRUE\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/hal/release/libble_host_hal_series2.a}\&quot;:\&quot;TRUE\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/bgstack/release/libble_host.a}\&quot;:\&quot;TRUE\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/ble_bgapi/release/libble_bgapi.a}\&quot;:\&quot;TRUE\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/system/release/libble_host_system.a}\&quot;:\&quot;TRUE\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/bgstack/ll/build/gcc/xg21/release/liblinklayer.a}\&quot;:\&quot;TRUE\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/ble_system/release/libble_system.a}\&quot;:\&quot;TRUE\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/bgcommon/lib/build/gcc/cortex-m33/bgcommon/release/libbgcommon.a}\&quot;:\&quot;TRUE\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/ble_bgapi/release/libble_bgapi_gatt_server.a}\&quot;:\&quot;TRUE\&quot;}},{\&quot;listValues\&quot;:[],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.base\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.optimization.datasect\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;listValues\&quot;:[],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;gnu.cpp.compiler.option.warnings.allwarn\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;listValues\&quot;:[],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.base\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.floatingpoint.enable\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;listValues\&quot;:[\&quot;-Wl,--wrap=_free_r -Wl,--wrap=_malloc_r -Wl,--wrap=_calloc_r -Wl,--wrap=_realloc_r\&quot;,\&quot;-fno-lto\&quot;,\&quot;-Wl,--no-warn-rwx-segments\&quot;],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.base\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.gnu.cpp.link.option.flags\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;listValuesMap\&quot;:{\&quot;-Wl,--wrap=_free_r -Wl,--wrap=_malloc_r -Wl,--wrap=_calloc_r -Wl,--wrap=_realloc_r\&quot;:\&quot;TRUE\&quot;,\&quot;-fno-lto\&quot;:\&quot;TRUE\&quot;,\&quot;-Wl,--no-warn-rwx-segments\&quot;:\&quot;TRUE\&quot;}},{\&quot;listValues\&quot;:[],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.optimization.omitframepointer\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;listValues\&quot;:[],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.base\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.map\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;listValues\&quot;:[],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.base\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.floatingpoint.type\&quot;,\&quot;value\&quot;:\&quot;floatingpoint.type.hard\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;listValues\&quot;:[],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.assembler.base\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.assembler.floatingpoint.enable\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;listValues\&quot;:[],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.optimization.datasect\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;listValues\&quot;:[],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.base\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;gnu.c.compiler.option.warnings.toerrors\&quot;,\&quot;value\&quot;:\&quot;false\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;listValues\&quot;:[],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.optimization.functionsects\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;listValues\&quot;:[],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.base\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;gnu.c.compiler.option.warnings.extrawarn\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;listValues\&quot;:[],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.base\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.floatingpoint.type\&quot;,\&quot;value\&quot;:\&quot;floatingpoint.type.hard\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;listValues\&quot;:[],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.toolchain.exe\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.script\&quot;,\&quot;value\&quot;:\&quot;\\\&quot;${workspace_loc:/${ProjName}/autogen/linkerfile.ld}\\\&quot;\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;listValues\&quot;:[],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.base\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.floatingpoint.type\&quot;,\&quot;value\&quot;:\&quot;floatingpoint.type.hard\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;listValues\&quot;:[],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.optimization.level\&quot;,\&quot;value\&quot;:\&quot;gnu.cpp.compiler.optimization.level.size\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;listValues\&quot;:[],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.base\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;gnu.c.compiler.option.warnings.allwarn\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;listValues\&quot;:[],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;gnu.cpp.compiler.option.warnings.toerrors\&quot;,\&quot;value\&quot;:\&quot;false\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;listValues\&quot;:[],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.floatingpoint.enable\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;listValues\&quot;:[],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.base\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.nostdlibs\&quot;,\&quot;value\&quot;:\&quot;false\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;listValues\&quot;:[],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.base\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.optimization.level\&quot;,\&quot;value\&quot;:\&quot;gnu.c.optimization.level.size\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;listValues\&quot;:[],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.no_rtti\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;listValues\&quot;:[],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.assembler.base\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.assembler.floatingpoint.type\&quot;,\&quot;value\&quot;:\&quot;floatingpoint.type.hard\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;listValues\&quot;:[],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.floatingpoint.type\&quot;,\&quot;value\&quot;:\&quot;floatingpoint.type.hard\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;listValues\&quot;:[],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.base\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.clibs\&quot;,\&quot;value\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.nanospec\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;listValues\&quot;:[],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.base\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.circulardependency\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;listValues\&quot;:[],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.no_exceptions\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;listValues\&quot;:[\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/bgstack/release/libbondingdb.a}\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/connection_subrating/release/libble_host_connection_subrating_stub.a}\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/bgapi_protocol/api3/release/libbgapi_core.a}\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/radio/rail_lib/autogen/librail_release/librail_efr32xg21_gcc_release.a}\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/ble_bgapi/release/libble_bgapi_gatt_client.a}\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/accept_list/release/libble_host_accept_list_stub.a}\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/hci/release/libble_host_hci.a}\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/core/release/libble_host_core.a}\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/hal/release/libble_host_hal_series2.a}\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/bgstack/release/libble_host.a}\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/ble_bgapi/release/libble_bgapi.a}\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/system/release/libble_host_system.a}\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/bgstack/ll/build/gcc/xg21/release/liblinklayer.a}\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/ble_system/release/libble_system.a}\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/bgcommon/lib/build/gcc/cortex-m33/bgcommon/release/libbgcommon.a}\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/ble_bgapi/release/libble_bgapi_gatt_server.a}\&quot;],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.base\&quot;,\&quot;builtin\&quot;:false,\&quot;optionId\&quot;:\&quot;gnu.cpp.link.option.userobjs\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;listValuesMap\&quot;:{\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/bgstack/release/libbondingdb.a}\&quot;:\&quot;TRUE\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/connection_subrating/release/libble_host_connection_subrating_stub.a}\&quot;:\&quot;TRUE\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/bgapi_protocol/api3/release/libbgapi_core.a}\&quot;:\&quot;TRUE\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/radio/rail_lib/autogen/librail_release/librail_efr32xg21_gcc_release.a}\&quot;:\&quot;TRUE\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/ble_bgapi/release/libble_bgapi_gatt_client.a}\&quot;:\&quot;TRUE\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/accept_list/release/libble_host_accept_list_stub.a}\&quot;:\&quot;TRUE\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/hci/release/libble_host_hci.a}\&quot;:\&quot;TRUE\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/core/release/libble_host_core.a}\&quot;:\&quot;TRUE\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/hal/release/libble_host_hal_series2.a}\&quot;:\&quot;TRUE\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/bgstack/release/libble_host.a}\&quot;:\&quot;TRUE\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/ble_bgapi/release/libble_bgapi.a}\&quot;:\&quot;TRUE\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/system/release/libble_host_system.a}\&quot;:\&quot;TRUE\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/bgstack/ll/build/gcc/xg21/release/liblinklayer.a}\&quot;:\&quot;TRUE\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/ble_system/release/libble_system.a}\&quot;:\&quot;TRUE\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/bgcommon/lib/build/gcc/cortex-m33/bgcommon/release/libbgcommon.a}\&quot;:\&quot;TRUE\&quot;,\&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/ble_bgapi/release/libble_bgapi_gatt_server.a}\&quot;:\&quot;TRUE\&quot;}},{\&quot;listValues\&quot;:[],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.base\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.circulardependency\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;listValues\&quot;:[],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.base\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.generateSecureCode\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;listValues\&quot;:[],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.base\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.optimization.omitframepointer\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;listValues\&quot;:[],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.base\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.misc.dialect\&quot;,\&quot;value\&quot;:\&quot;gnu.c.compiler.dialect.c18\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;listValues\&quot;:[],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.base\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.floatingpoint.enable\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;listValues\&quot;:[],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;gnu.cpp.compiler.option.warnings.extrawarn\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;listValues\&quot;:[\&quot;-Wl,--wrap=_free_r -Wl,--wrap=_malloc_r -Wl,--wrap=_calloc_r -Wl,--wrap=_realloc_r\&quot;,\&quot;-fno-lto\&quot;,\&quot;-Wl,--no-warn-rwx-segments\&quot;],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.base\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.gnu.c.link.option.ldflags\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;listValuesMap\&quot;:{\&quot;-Wl,--wrap=_free_r -Wl,--wrap=_malloc_r -Wl,--wrap=_calloc_r -Wl,--wrap=_realloc_r\&quot;:\&quot;TRUE\&quot;,\&quot;-fno-lto\&quot;:\&quot;TRUE\&quot;,\&quot;-Wl,--no-warn-rwx-segments\&quot;:\&quot;TRUE\&quot;}},{\&quot;listValues\&quot;:[],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.base\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.optimization.functionsects\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;listValues\&quot;:[],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.misc.dialect\&quot;,\&quot;value\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.misc.dialect.cpp17\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;listValues\&quot;:[],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.toolchain.exe\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.usescript\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;listValues\&quot;:[],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.generateSecureCode\&quot;,\&quot;value\&quot;:\&quot;true\&quot;,\&quot;listValuesMap\&quot;:{}},{\&quot;listValues\&quot;:[\&quot;-fno-lto\&quot;,\&quot;--specs=nano.specs\&quot;,\&quot;-fmessage-length=0\&quot;,\&quot;-c\&quot;],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.gnu.cpp.compiler.option.misc.otherlist\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;listValuesMap\&quot;:{\&quot;-fno-lto\&quot;:\&quot;TRUE\&quot;,\&quot;--specs=nano.specs\&quot;:\&quot;TRUE\&quot;,\&quot;-fmessage-length=0\&quot;:\&quot;TRUE\&quot;,\&quot;-c\&quot;:\&quot;TRUE\&quot;}},{\&quot;listValues\&quot;:[\&quot;-fno-lto\&quot;,\&quot;--specs=nano.specs\&quot;,\&quot;-fmessage-length=0\&quot;,\&quot;-c\&quot;],\&quot;toolId\&quot;:\&quot;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.base\&quot;,\&quot;builtin\&quot;:true,\&quot;optionId\&quot;:\&quot;com.silabs.gnu.c.compiler.option.misc.otherlist\&quot;,\&quot;value\&quot;:\&quot;\&quot;,\&quot;listValuesMap\&quot;:{\&quot;-fno-lto\&quot;:\&quot;TRUE\&quot;,\&quot;--specs=nano.specs\&quot;:\&quot;TRUE\&quot;,\&quot;-fmessage-length=0\&quot;:\&quot;TRUE\&quot;,\&quot;-c\&quot;:\&quot;TRUE\&quot;}}]&quot;}]" moduleId="com.silabs.ss.framework.ide.project.core.cpp" projectCommon.boardIds="com.silabs.board.none:0.0.0" projectCommon.buildArtifactType="EXE" projectCommon.partId="mcu.arm.efr32.bg21.efr32bg21a020f768im32" projectCommon.referencedModules="[{&quot;removed&quot;:false,&quot;builtinExcludes&quot;:[],&quot;builtinSources&quot;:[&quot;config/app_assert_config.h&quot;,&quot;config/app_properties_config.h&quot;,&quot;config/btconf/gatt_configuration.btconf&quot;,&quot;config/btconf/in_place_ota_dfu.xml&quot;,&quot;config/btl_interface_cfg.h&quot;,&quot;config/nvm3_default_config.h&quot;,&quot;config/psa_crypto_config.h&quot;,&quot;config/sl_bgapi_config.h&quot;,&quot;config/sl_bluetooth_advertiser_config.h&quot;,&quot;config/sl_bluetooth_config.h&quot;,&quot;config/sl_bluetooth_connection_config.h&quot;,&quot;config/sl_bt_host_adaptation_config.h&quot;,&quot;config/sl_bt_in_place_ota_dfu_config.h&quot;,&quot;config/sl_btctrl_config.h&quot;,&quot;config/sl_btctrl_scheduler_priority_config.h&quot;,&quot;config/sl_clock_manager_oscillator_config.h&quot;,&quot;config/sl_clock_manager_tree_config.h&quot;,&quot;config/sl_core_config.h&quot;,&quot;config/sl_mbedtls_config.h&quot;,&quot;config/sl_mbedtls_device_config.h&quot;,&quot;config/sl_memory_manager_config.h&quot;,&quot;config/sl_memory_manager_region_config.h&quot;,&quot;config/sl_power_manager_config.h&quot;,&quot;config/sl_rail_util_pa_config.h&quot;,&quot;config/sl_rail_util_power_manager_init_config.h&quot;,&quot;config/sl_rail_util_pti_config.h&quot;,&quot;config/sl_rail_util_rf_path_config.h&quot;,&quot;config/sl_sleeptimer_config.h&quot;],&quot;module&quot;:&quot;&lt;project:MModule xmlns:project=\&quot;http://www.silabs.com/ss/Project.ecore\&quot; builtin=\&quot;true\&quot; id=\&quot;uc.module.setup.ucConfig.com.silabs.ss.framework.project.toolchain.core.default#com.silabs.ss.tool.ide.arm.toolchain.gnu.cdt:12.2.1.20221205.gcc\&quot; pdm=\&quot;true\&quot;&gt;\r\n  &lt;inclusions pattern=\&quot;.*\&quot;/&gt;\r\n&lt;/project:MModule&gt;&quot;,&quot;builtin&quot;:true},{&quot;removed&quot;:false,&quot;builtinExcludes&quot;:[],&quot;builtinSources&quot;:[&quot;autogen/RTE_Components.h&quot;,&quot;autogen/linkerfile.ld&quot;,&quot;autogen/sl_application_type.h&quot;,&quot;autogen/sl_bluetooth.c&quot;,&quot;autogen/sl_bluetooth.h&quot;,&quot;autogen/sl_component_catalog.h&quot;,&quot;autogen/sl_event_handler.c&quot;,&quot;autogen/sl_event_handler.h&quot;,&quot;autogen/sl_power_manager_handler.c&quot;,&quot;autogen/sli_mbedtls_config_autogen.h&quot;,&quot;autogen/sli_mbedtls_config_transform_autogen.h&quot;,&quot;autogen/sli_psa_builtin_config_autogen.h&quot;,&quot;autogen/sli_psa_config_autogen.h&quot;],&quot;module&quot;:&quot;&lt;project:MModule xmlns:project=\&quot;http://www.silabs.com/ss/Project.ecore\&quot; builtin=\&quot;true\&quot; id=\&quot;uc.module.setup.ucTemplate.com.silabs.ss.framework.project.toolchain.core.default#com.silabs.ss.tool.ide.arm.toolchain.gnu.cdt:12.2.1.20221205.gcc\&quot; pdm=\&quot;true\&quot;&gt;\r\n  &lt;inclusions pattern=\&quot;.*\&quot;/&gt;\r\n&lt;/project:MModule&gt;&quot;,&quot;builtin&quot;:true},{&quot;removed&quot;:false,&quot;builtinExcludes&quot;:[],&quot;builtinSources&quot;:[&quot;autogen/gen.properties&quot;],&quot;module&quot;:&quot;&lt;project:MModule xmlns:project=\&quot;http://www.silabs.com/ss/Project.ecore\&quot; builtin=\&quot;true\&quot; id=\&quot;uc.module.setup.projectMetadata.com.silabs.ss.framework.project.toolchain.core.default#com.silabs.ss.tool.ide.arm.toolchain.gnu.cdt:12.2.1.20221205.gcc\&quot; pdm=\&quot;true\&quot;&gt;\r\n  &lt;inclusions pattern=\&quot;.*\&quot;/&gt;\r\n&lt;/project:MModule&gt;&quot;,&quot;builtin&quot;:true},{&quot;removed&quot;:false,&quot;builtinExcludes&quot;:[&quot;image/readme_img0.png&quot;,&quot;image/readme_img1.png&quot;,&quot;image/readme_img2.png&quot;,&quot;image/readme_img3.png&quot;,&quot;image/readme_img4.png&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/api/sl_bt.xapi&quot;],&quot;builtinSources&quot;:[&quot;app.c&quot;,&quot;app.h&quot;,&quot;app_bm.c&quot;,&quot;image/readme_img0.png&quot;,&quot;image/readme_img1.png&quot;,&quot;image/readme_img2.png&quot;,&quot;image/readme_img3.png&quot;,&quot;image/readme_img4.png&quot;,&quot;main.c&quot;,&quot;readme.md&quot;,&quot;simplicity_sdk_2025.6.1/app/bluetooth/common/gatt_service_device_information_override/sl_gatt_service_device_information_override.h&quot;,&quot;simplicity_sdk_2025.6.1/app/bluetooth/common/in_place_ota_dfu/sl_bt_in_place_ota_dfu.c&quot;,&quot;simplicity_sdk_2025.6.1/app/bluetooth/common/in_place_ota_dfu/sl_bt_in_place_ota_dfu.h&quot;,&quot;simplicity_sdk_2025.6.1/app/common/util/app_assert/app_assert.h&quot;,&quot;simplicity_sdk_2025.6.1/app/common/util/app_assert/sl_app_assert.h&quot;,&quot;simplicity_sdk_2025.6.1/app/common/util/app_timer/app_timer.h&quot;,&quot;simplicity_sdk_2025.6.1/app/common/util/app_timer/bm/app_timer.c&quot;,&quot;simplicity_sdk_2025.6.1/app/common/util/app_timer/bm/app_timer_internal.h&quot;,&quot;simplicity_sdk_2025.6.1/app/common/util/app_timer/bm/app_timer_types.h&quot;,&quot;simplicity_sdk_2025.6.1/app/common/util/app_timer/sl_simple_timer.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/CMSIS/Core/Include/cmsis_compiler.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/CMSIS/Core/Include/cmsis_gcc.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/CMSIS/Core/Include/cmsis_version.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/CMSIS/Core/Include/core_cm33.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/CMSIS/Core/Include/mpu_armv8.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/CMSIS/Core/Include/tz_context.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_acmp.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_aes.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_bufc.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_buram.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_burtc.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_cmu.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_devinfo.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_dma_descriptor.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_dpll.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_emu.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_fsrco.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_gpcrc.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_gpio.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_gpio_port.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_hfrco.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_hfxo.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_i2c.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_iadc.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_icache.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_ldma.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_ldmaxbar.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_ldmaxbar_defines.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_letimer.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_lfrco.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_lfxo.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_lvgd.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_msc.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_prs.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_prs_signals.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_rtcc.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_semailbox.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_smu.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_syscfg.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_timer.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_ulfrco.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_usart.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_wdog.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21a020f768im32.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/em_device.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/system_efr32bg21.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Source/startup_efr32bg21.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Source/system_efr32bg21.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/bootloader/api/application_properties.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/bootloader/api/btl_errorcode.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/bootloader/api/btl_interface.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/bootloader/api/btl_interface.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/bootloader/api/btl_interface_parser.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/bootloader/api/btl_interface_storage.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/bootloader/api/btl_interface_storage.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/bootloader/api/btl_reset_info.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/bootloader/app_properties/app_properties.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/bootloader/core/btl_util.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/bootloader/core/flash/btl_internal_flash.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/bootloader/core/flash/btl_internal_flash.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/common/inc/sl_assert.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/common/inc/sl_atomic.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/common/inc/sl_bit.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/common/inc/sl_code_classification.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/common/inc/sl_common.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/common/inc/sl_compiler.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/common/inc/sl_core.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/common/inc/sl_enum.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/common/inc/sl_slist.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/common/inc/sl_status.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/common/inc/sli_code_classification.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/common/src/sl_assert.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/common/src/sl_core_cortexm.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/common/src/sl_slist.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/common/src/sl_syscalls.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/driver/gpio/inc/sl_gpio.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/driver/gpio/src/sl_gpio.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/emdrv/common/inc/ecode.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/emdrv/nvm3/config/nvm3_config.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/emdrv/nvm3/inc/nvm3.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/emdrv/nvm3/inc/nvm3_cache.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/emdrv/nvm3/inc/nvm3_default.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/emdrv/nvm3/inc/nvm3_generic.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/emdrv/nvm3/inc/nvm3_hal.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/emdrv/nvm3/inc/nvm3_hal_flash.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/emdrv/nvm3/inc/nvm3_lock.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/emdrv/nvm3/inc/nvm3_object.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/emdrv/nvm3/inc/nvm3_page.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/emdrv/nvm3/inc/nvm3_trace.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/emdrv/nvm3/inc/nvm3_utils.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/emdrv/nvm3/src/nvm3.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/emdrv/nvm3/src/nvm3_cache.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/emdrv/nvm3/src/nvm3_default_common_linker.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/emdrv/nvm3/src/nvm3_hal_flash.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/emdrv/nvm3/src/nvm3_lock.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/emdrv/nvm3/src/nvm3_object.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/emdrv/nvm3/src/nvm3_page.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/emdrv/nvm3/src/nvm3_utils.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/emlib/inc/em_assert.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/emlib/inc/em_burtc.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/emlib/inc/em_bus.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/emlib/inc/em_chip.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/emlib/inc/em_cmu.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/emlib/inc/em_cmu_compat.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/emlib/inc/em_common.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/emlib/inc/em_core.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/emlib/inc/em_core_generic.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/emlib/inc/em_emu.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/emlib/inc/em_gpio.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/emlib/inc/em_msc.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/emlib/inc/em_msc_compat.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/emlib/inc/em_prs.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/emlib/inc/em_ramfunc.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/emlib/inc/em_rtcc.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/emlib/inc/em_syscfg.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/emlib/inc/em_system.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/emlib/inc/em_system_generic.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/emlib/inc/em_timer.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/emlib/inc/em_version.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/emlib/inc/sli_em_cmu.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/emlib/src/em_burtc.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/emlib/src/em_cmu.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/emlib/src/em_emu.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/emlib/src/em_gpio.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/emlib/src/em_msc.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/emlib/src/em_prs.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/emlib/src/em_rtcc.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/emlib/src/em_system.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/emlib/src/em_timer.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/peripheral/inc/sl_hal_bus.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/peripheral/inc/sl_hal_gpio.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/peripheral/inc/sl_hal_syscfg.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/peripheral/inc/sl_hal_system.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/peripheral/inc/sl_hal_system_generic.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/peripheral/src/sl_hal_gpio.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/peripheral/src/sl_hal_system.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/autogen/librail_release/librail_efr32xg21_gcc_release.a&quot;,&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/chip/efr32/efr32xg2x/rail_chip_specific.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/chip/efr32/efr32xg2x/sl_rail_chip_specific.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/common/rail.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/common/rail_assert_error_codes.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/common/rail_features.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/common/rail_mfm.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/common/rail_types.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/common/sl_rail.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/common/sl_rail_assert_error_codes.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/common/sl_rail_features.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/common/sl_rail_mfm.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/common/sl_rail_types.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/pa-conversions/efr32xg21/sl_rail_util_pa_curves.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/pa-conversions/pa_conversions_efr32.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/pa-conversions/pa_conversions_efr32.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/pa-conversions/pa_curve_types_efr32.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/pa-conversions/pa_curves_efr32.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/pa-conversions/pa_curves_efr32.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/pa-conversions/sl_rail_util_pa_conversions_efr32.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/pa-conversions/sl_rail_util_pa_curve_types_efr32.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/rail_util_power_manager_init/sl_rail_util_power_manager_init.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/rail_util_power_manager_init/sl_rail_util_power_manager_init.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/rail_util_pti/sl_rail_util_pti.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/rail_util_pti/sl_rail_util_pti.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/rail_util_rf_path/sl_rail_util_rf_path.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/rail_util_rf_path/sl_rail_util_rf_path.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/protocol/ble/rail_ble.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/protocol/ble/sl_rail_ble.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/protocol/ieee802154/rail_ieee802154.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/protocol/ieee802154/sl_rail_ieee802154.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/protocol/sidewalk/rail_sidewalk.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/protocol/sidewalk/sl_rail_sidewalk.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/protocol/wmbus/rail_wmbus.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/protocol/wmbus/sl_rail_wmbus.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/protocol/zwave/rail_zwave.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/protocol/zwave/sl_rail_zwave.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/inc/sl_se_manager.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/inc/sl_se_manager_attestation.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/inc/sl_se_manager_check_config.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/inc/sl_se_manager_cipher.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/inc/sl_se_manager_config.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/inc/sl_se_manager_defines.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/inc/sl_se_manager_entropy.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/inc/sl_se_manager_hash.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/inc/sl_se_manager_internal_keys.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/inc/sl_se_manager_key_derivation.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/inc/sl_se_manager_key_handling.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/inc/sl_se_manager_signature.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/inc/sl_se_manager_types.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/inc/sl_se_manager_util.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/inc/sli_se_manager_features.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/inc/sli_se_manager_internal.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/inc/sli_se_manager_mailbox.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/src/sl_se_manager.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/src/sl_se_manager_attestation.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/src/sl_se_manager_cipher.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/src/sl_se_manager_entropy.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/src/sl_se_manager_hash.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/src/sl_se_manager_key_derivation.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/src/sl_se_manager_key_handling.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/src/sl_se_manager_signature.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/src/sl_se_manager_util.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/src/sli_se_manager_mailbox.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/config/sli_mbedtls_acceleration.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/config/sli_mbedtls_omnipresent.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/config/sli_psa_acceleration.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/inc/aes_alt.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/inc/ccm_alt.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/inc/cmac_alt.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/inc/ecjpake_alt.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/inc/gcm_alt.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/inc/se_management.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/inc/sha1_alt.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/inc/sha256_alt.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/inc/sha512_alt.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/inc/sl_mbedtls.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/inc/sl_psa_crypto.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/inc/sl_psa_values.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/inc/sli_psa_crypto.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/src/sl_mbedtls.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/src/sl_psa_crypto.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/src/sli_psa_crypto.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_protocol_crypto/src/sli_protocol_crypto.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_protocol_crypto/src/sli_protocol_crypto_radioaes.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_protocol_crypto/src/sli_radioaes_management.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_protocol_crypto/src/sli_radioaes_management.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/inc/psa/error.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/inc/psa/internal_trusted_storage.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/inc/psa/sli_internal_trusted_storage.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/inc/sli_psa_driver_common.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/inc/sli_psa_driver_features.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/inc/sli_se_driver_aead.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/inc/sli_se_driver_cipher.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/inc/sli_se_driver_key_derivation.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/inc/sli_se_driver_key_management.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/inc/sli_se_driver_mac.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/inc/sli_se_opaque_functions.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/inc/sli_se_opaque_types.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/inc/sli_se_transparent_functions.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/inc/sli_se_transparent_types.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/inc/sli_se_version_dependencies.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/src/sl_psa_its_nvm3.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/src/sli_psa_driver_common.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/src/sli_psa_driver_init.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/src/sli_psa_trng.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/src/sli_se_driver_aead.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/src/sli_se_driver_builtin_keys.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/src/sli_se_driver_cipher.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/src/sli_se_driver_key_derivation.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/src/sli_se_driver_key_management.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/src/sli_se_driver_mac.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/src/sli_se_driver_signature.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/src/sli_se_opaque_driver_aead.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/src/sli_se_opaque_driver_cipher.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/src/sli_se_opaque_driver_mac.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/src/sli_se_opaque_key_derivation.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/src/sli_se_transparent_driver_aead.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/src/sli_se_transparent_driver_cipher.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/src/sli_se_transparent_driver_hash.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/src/sli_se_transparent_driver_mac.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/src/sli_se_transparent_key_derivation.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/src/sli_se_version_dependencies.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sli_crypto/inc/sli_crypto.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sli_crypto/inc/sli_crypto_s2.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sli_crypto/src/sl_crypto_s2.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sli_psec_osal/inc/sli_psec_osal.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sli_psec_osal/inc/sli_psec_osal_baremetal.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/clock_manager/inc/sl_clock_manager.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/clock_manager/inc/sl_clock_manager_init.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/clock_manager/inc/sli_clock_manager.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/clock_manager/src/sl_clock_manager.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/clock_manager/src/sl_clock_manager_hal_s2.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/clock_manager/src/sl_clock_manager_init.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/clock_manager/src/sl_clock_manager_init_hal_s2.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/clock_manager/src/sli_clock_manager_hal.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/clock_manager/src/sli_clock_manager_init_hal.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/device_manager/devices/sl_device_peripheral_hal_efr32xg21.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/device_manager/inc/sl_device_clock.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/device_manager/inc/sl_device_gpio.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/device_manager/inc/sl_device_i2c.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/device_manager/inc/sl_device_peripheral.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/device_manager/inc/sl_device_peripheral_types.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/device_manager/src/sl_device_clock.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/device_manager/src/sl_device_gpio.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/device_manager/src/sl_device_peripheral.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/interrupt_manager/inc/arm/cmsis_nvic_virtual.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/interrupt_manager/inc/sl_interrupt_manager.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/interrupt_manager/src/sl_interrupt_manager_cortexm.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/interrupt_manager/src/sli_interrupt_manager.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/memory_manager/inc/sl_memory_manager.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/memory_manager/inc/sl_memory_manager_region.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/memory_manager/inc/sli_memory_manager_retention_control.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/memory_manager/profiler/inc/sli_memory_profiler.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/memory_manager/profiler/src/sli_memory_profiler_stubs.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/memory_manager/src/sl_memory_manager.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/memory_manager/src/sl_memory_manager_dynamic_reservation.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/memory_manager/src/sl_memory_manager_pool.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/memory_manager/src/sl_memory_manager_pool_common.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/memory_manager/src/sl_memory_manager_region.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/memory_manager/src/sl_memory_manager_retarget.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/memory_manager/src/sli_memory_manager.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/memory_manager/src/sli_memory_manager_common.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/mpu/inc/sl_mpu.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/mpu/src/sl_mpu_s2.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/power_manager/inc/sl_power_manager.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/power_manager/inc/sl_power_manager_debug.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/power_manager/inc/sli_power_manager.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/power_manager/src/common/sl_power_manager_common.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/power_manager/src/common/sl_power_manager_em4.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/power_manager/src/sleep_loop/sl_power_manager.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/power_manager/src/sleep_loop/sl_power_manager_debug.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/power_manager/src/sleep_loop/sl_power_manager_hal_s2.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/power_manager/src/sleep_loop/sli_power_manager_private.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/sl_main/inc/sl_main_init.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/sl_main/inc/sl_main_init_memory.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/sl_main/inc/sl_main_process_action.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/sl_main/src/sl_main_init.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/sl_main/src/sl_main_init_memory.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/sl_main/src/sl_main_process_action.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/sl_main/src/sli_main_init_memory.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/sleeptimer/inc/sl_sleeptimer.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/sleeptimer/inc/sli_sleeptimer.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/sleeptimer/src/sl_sleeptimer.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/sleeptimer/src/sl_sleeptimer_hal_burtc.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/sleeptimer/src/sl_sleeptimer_hal_prortc.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/sleeptimer/src/sl_sleeptimer_hal_rtcc.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/sleeptimer/src/sl_sleeptimer_hal_timer.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/sleeptimer/src/sli_sleeptimer_hal.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/udelay/inc/sl_udelay.h&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/udelay/src/sl_udelay.c&quot;,&quot;simplicity_sdk_2025.6.1/platform/service/udelay/src/sl_udelay_armv6m_gcc.S&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/api/sl_bt.xapi&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/bgcommon/inc/sli_bgcommon_debug.h&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/bgcommon/lib/build/gcc/cortex-m33/bgcommon/release/libbgcommon.a&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/bgcommon/src/sli_bgcommon_debug_efr32.c&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/bgstack/ll/build/gcc/xg21/release/liblinklayer.a&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/bgstack/ll/inc/sl_bt_ll_config.h&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/bgstack/ll/inc/sl_btctrl_callbacks.h&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/bgstack/ll/inc/sl_btctrl_linklayer.h&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/bgstack/ll/inc/sl_btctrl_linklayer_defs.h&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/bgstack/ll/inc/sl_btctrl_packets.h&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/bgstack/ll/src/sl_btctrl_init.c&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/bgstack/ll/src/sl_btctrl_init_tasklets.c&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/bgapi_protocol/api3/release/libbgapi_core.a&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/accept_list/release/libble_host_accept_list_stub.a&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/bgstack/release/libble_host.a&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/bgstack/release/libbondingdb.a&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/ble_bgapi/release/libble_bgapi.a&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/ble_bgapi/release/libble_bgapi_gatt_client.a&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/ble_bgapi/release/libble_bgapi_gatt_server.a&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/ble_system/release/libble_system.a&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/connection_subrating/release/libble_host_connection_subrating_stub.a&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/core/release/libble_host_core.a&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/hal/release/libble_host_hal_series2.a&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/hci/release/libble_host_hci.a&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/system/release/libble_host_system.a&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sl_apploader_util.h&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sl_bgapi.h&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sl_bt_api.h&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sl_bt_api_compatibility.h&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sl_bt_ll_config.h&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sl_bt_stack_config.h&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sl_bt_stack_init.h&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sl_bt_types.h&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sl_bt_version.h&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sli_bgapi.h&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sli_bgapi_rtos_adaptation.h&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sli_bt_accept_list_config.h&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sli_bt_advertiser_config.h&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sli_bt_api.h&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sli_bt_config_defs.h&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sli_bt_connection_config.h&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sli_bt_dynamic_gattdb_config.h&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sli_bt_external_bondingdb_config.h&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sli_bt_gattdb_def.h&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sli_bt_host_adaptation.h&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sli_bt_l2cap_config.h&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sli_bt_pawr_advertiser_config.h&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sli_bt_periodic_advertiser_config.h&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sli_bt_sync_config.h&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/src/sl_apploader_util_s2.c&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/src/sl_bt_stack_init.c&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/src/sli_bt_accept_list_config.c&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/src/sli_bt_advertiser_config.c&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/src/sli_bt_connection_config.c&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/src/sli_bt_dynamic_gattdb_config.c&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/src/sli_bt_external_bondingdb_config.c&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/src/sli_bt_host_adaptation.c&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/src/sli_bt_l2cap_config.c&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/src/sli_bt_pawr_advertiser_config.c&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/src/sli_bt_periodic_advertiser_config.c&quot;,&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/src/sli_bt_sync_config.c&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/aes.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/aria.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/asn1.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/asn1write.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/base64.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/bignum.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/block_cipher.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/build_info.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/camellia.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/ccm.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/chacha20.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/chachapoly.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/check_config.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/cipher.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/cmac.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/compat-2.x.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/config_adjust_legacy_crypto.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/config_adjust_legacy_from_psa.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/config_adjust_psa_from_legacy.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/config_adjust_psa_superset_legacy.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/config_adjust_ssl.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/config_adjust_x509.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/config_psa.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/constant_time.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/ctr_drbg.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/debug.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/des.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/dhm.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/ecdh.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/ecdsa.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/ecjpake.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/ecp.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/entropy.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/error.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/gcm.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/hkdf.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/hmac_drbg.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/lms.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/mbedtls_config.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/md.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/md5.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/memory_buffer_alloc.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/net_sockets.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/nist_kw.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/oid.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/pem.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/pk.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/pkcs12.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/pkcs5.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/pkcs7.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/platform.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/platform_time.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/platform_util.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/poly1305.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/private_access.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/psa_util.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/ripemd160.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/rsa.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/sha1.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/sha256.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/sha3.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/sha512.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/ssl.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/ssl_cache.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/ssl_ciphersuites.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/ssl_cookie.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/ssl_ticket.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/threading.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/timing.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/version.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/x509.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/x509_crl.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/x509_crt.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/x509_csr.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/build_info.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/crypto.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/crypto_adjust_auto_enabled.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/crypto_adjust_config_dependencies.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/crypto_adjust_config_key_pair_types.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/crypto_adjust_config_synonyms.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/crypto_builtin_composites.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/crypto_builtin_key_derivation.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/crypto_builtin_primitives.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/crypto_compat.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/crypto_config.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/crypto_driver_common.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/crypto_driver_contexts_composites.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/crypto_driver_contexts_key_derivation.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/crypto_driver_contexts_primitives.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/crypto_extra.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/crypto_legacy.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/crypto_platform.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/crypto_se_driver.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/crypto_sizes.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/crypto_struct.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/crypto_types.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/crypto_values.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/alignment.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/base64_internal.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/bignum_core.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/bignum_internal.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/bignum_mod.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/bignum_mod_raw.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/bignum_mod_raw_invasive.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/block_cipher_internal.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/bn_mul.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/check_crypto_config.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/cipher.c&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/cipher_wrap.c&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/cipher_wrap.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/common.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/constant_time.c&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/constant_time_impl.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/constant_time_internal.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/ctr.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/debug_internal.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/ecp_internal_alt.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/ecp_invasive.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/entropy_poll.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/lmots.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/md_psa.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/md_wrap.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/mps_common.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/mps_error.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/mps_reader.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/mps_trace.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/padlock.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/pk_internal.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/pk_wrap.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/pkwrite.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/platform.c&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/platform_util.c&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto.c&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_aead.c&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_aead.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_cipher.c&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_cipher.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_client.c&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_core.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_core_common.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_driver_wrappers.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_driver_wrappers_no_static.c&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_driver_wrappers_no_static.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_ecp.c&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_ecp.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_ffdh.c&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_ffdh.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_hash.c&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_hash.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_invasive.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_its.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_mac.c&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_mac.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_pake.c&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_pake.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_random_impl.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_rsa.c&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_rsa.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_se.c&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_se.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_slot_management.c&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_slot_management.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_storage.c&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_storage.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_util.c&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_util_internal.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/rsa_alt_helpers.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/rsa_internal.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/ssl_ciphersuites_internal.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/ssl_client.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/ssl_debug_helpers.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/ssl_misc.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/ssl_tls13_invasive.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/ssl_tls13_keys.h&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/threading.c&quot;,&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/x509_internal.h&quot;,&quot;sl_gatt_service_device_information_override.c&quot;],&quot;module&quot;:&quot;&lt;project:MModule xmlns:project=\&quot;http://www.silabs.com/ss/Project.ecore\&quot; builtin=\&quot;true\&quot; id=\&quot;uc.module.setup.componentSetup.com.silabs.ss.framework.project.toolchain.core.default#com.silabs.ss.tool.ide.arm.toolchain.gnu.cdt:12.2.1.20221205.gcc\&quot; pdm=\&quot;true\&quot;&gt;\r\n  &lt;inclusions pattern=\&quot;.*\&quot;/&gt;\r\n&lt;/project:MModule&gt;&quot;,&quot;builtin&quot;:true},{&quot;removed&quot;:false,&quot;builtinExcludes&quot;:[],&quot;builtinSources&quot;:[&quot;bt_soc_empty_6.slpb&quot;],&quot;module&quot;:&quot;&lt;project:MModule xmlns:project=\&quot;http://www.silabs.com/ss/Project.ecore\&quot; builtin=\&quot;true\&quot; id=\&quot;uc.module.setup.CommonProjectPostBuild.com.silabs.ss.framework.project.toolchain.core.default#com.silabs.ss.tool.ide.arm.toolchain.gnu.cdt:12.2.1.20221205.gcc\&quot; pdm=\&quot;true\&quot;&gt;\r\n  &lt;inclusions pattern=\&quot;.*\&quot;/&gt;\r\n&lt;/project:MModule&gt;&quot;,&quot;builtin&quot;:true},{&quot;removed&quot;:false,&quot;builtinExcludes&quot;:[],&quot;builtinSources&quot;:[],&quot;module&quot;:&quot;&lt;project:MModule xmlns:project=\&quot;http://www.silabs.com/ss/Project.ecore\&quot; builtin=\&quot;true\&quot; id=\&quot;uc.module.setup.defaultSettings.com.silabs.ss.framework.project.toolchain.core.default#com.silabs.ss.tool.ide.arm.toolchain.gnu.cdt:12.2.1.20221205.gcc\&quot; pdm=\&quot;true\&quot;&gt;\r\n  &lt;inclusions pattern=\&quot;.*\&quot;/&gt;\r\n&lt;/project:MModule&gt;&quot;,&quot;builtin&quot;:true},{&quot;removed&quot;:false,&quot;builtinExcludes&quot;:[],&quot;builtinSources&quot;:[],&quot;module&quot;:&quot;&lt;project:MModule xmlns:project=\&quot;http://www.silabs.com/ss/Project.ecore\&quot; builtin=\&quot;true\&quot; id=\&quot;uc.module.setup.ProjectPostBuild.com.silabs.ss.framework.project.toolchain.core.default#com.silabs.ss.tool.ide.arm.toolchain.gnu.cdt:12.2.1.20221205.gcc\&quot; pdm=\&quot;true\&quot;&gt;\r\n  &lt;inclusions pattern=\&quot;.*\&quot;/&gt;\r\n&lt;/project:MModule&gt;&quot;,&quot;builtin&quot;:true},{&quot;removed&quot;:false,&quot;builtinExcludes&quot;:[],&quot;builtinSources&quot;:[&quot;autogen/.slc_state/.crc_config.crc&quot;],&quot;module&quot;:&quot;&lt;project:MModule xmlns:project=\&quot;http://www.silabs.com/ss/Project.ecore\&quot; builtin=\&quot;true\&quot; id=\&quot;uc.module.setup.ucProject.com.silabs.ss.framework.project.toolchain.core.default#com.silabs.ss.tool.ide.arm.toolchain.gnu.cdt:12.2.1.20221205.gcc\&quot; pdm=\&quot;true\&quot;&gt;\r\n  &lt;inclusions pattern=\&quot;.*\&quot;/&gt;\r\n&lt;/project:MModule&gt;&quot;,&quot;builtin&quot;:true},{&quot;removed&quot;:false,&quot;builtinExcludes&quot;:[],&quot;builtinSources&quot;:[&quot;autogen/gatt_db.c&quot;,&quot;autogen/gatt_db.h&quot;],&quot;module&quot;:&quot;&lt;project:MModule xmlns:project=\&quot;http://www.silabs.com/ss/Project.ecore\&quot; builtin=\&quot;true\&quot; id=\&quot;uc.module.setup.apack_btConfig.com.silabs.ss.framework.project.toolchain.core.default#com.silabs.ss.tool.ide.arm.toolchain.gnu.cdt:12.2.1.20221205.gcc\&quot; pdm=\&quot;true\&quot;&gt;\r\n  &lt;inclusions pattern=\&quot;.*\&quot;/&gt;\r\n&lt;/project:MModule&gt;&quot;,&quot;builtin&quot;:true}]" projectCommon.savedStockVariables="{&quot;partOpn&quot;:&quot;efr32bg21a020f768im32&quot;,&quot;copiedSdkLocation&quot;:&quot;simplicity_sdk_2025.6.1&quot;}" projectCommon.sdkId="com.silabs.sdk.stack.sisdk:2025.6.1._309472562" projectCommon.toolchainId="com.silabs.ss.tool.ide.arm.toolchain.gnu.cdt:12.2.1.20221205"/>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactName="${ProjName}" buildArtefactType="org.eclipse.cdt.build.core.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildArtefactType=org.eclipse.cdt.build.core.buildArtefactType.exe" description="" id="com.silabs.ss.framework.project.toolchain.core.default#com.silabs.ss.tool.ide.arm.toolchain.gnu.cdt:12.2.1.20221205" name="GNU ARM v12.2.1 - Default" parent="com.silabs.ide.si32.gcc.cdt.managedbuild.config.gnu.exe" postbuildStep="&quot;${CommanderAdapterPackPath}&quot; postbuild &quot;${workspace_loc:/${ProjName}}/bt_soc_empty_6.slpb&quot; --parameter build_dir:&quot;${workspace_loc:/${ProjName}/${ConfigName}}&quot;">
					<folderInfo id="com.silabs.ss.framework.project.toolchain.core.default#com.silabs.ss.tool.ide.arm.toolchain.gnu.cdt:12.2.1.20221205." name="/" resourcePath="">
						<toolChain id="com.silabs.ide.si32.gcc.cdt.managedbuild.toolchain.exe.147964663" name="Si32 GNU ARM" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.toolchain.exe">
							<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.toolchain.debug.level.286014645" name="Debug Level" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.toolchain.debug.level" value="com.silabs.ide.si32.gcc.cdt.managedbuild.toolchain.debug.level.default" valueType="enumerated"/>
							<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.script.1302507334" name="Linker Script:" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.script" value="&quot;${workspace_loc:/${ProjName}/autogen/linkerfile.ld}&quot;" valueType="string"/>
							<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.usescript.832118812" name="Use custom linker script" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.usescript" value="true" valueType="boolean"/>
							<targetPlatform binaryParser="org.eclipse.cdt.core.ELF;org.eclipse.cdt.core.GNU_ELF;com.silabs.ss.framework.debugger.core.BIN;com.silabs.ss.framework.debugger.core.HEX;com.silabs.ss.framework.debugger.core.S37;com.silabs.ss.framework.debugger.core.EBL;com.silabs.ss.framework.debugger.core.GBL" id="com.silabs.ide.si32.gcc.cdt.managedbuild.target.gnu.platform.base.18627016" isAbstract="false" name="Debug Platform" osList="win32,linux,macosx" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.target.gnu.platform.base"/>
							<builder buildPath="${workspace_loc:/bt_soc_empty_6}/GNU ARM v12.2.1 - Default" id="com.silabs.ide.si32.gcc.cdt.managedbuild.target.gnu.builder.base.14456766" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="Si32 GNU ARM Builder" parallelBuildOn="true" parallelizationNumber="optimal" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.target.gnu.builder.base"/>
							<tool id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.base.807393319" name="GNU ARM C Compiler" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.base">
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.floatingpoint.type.1744689875" name="Floating-Point ABI" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.floatingpoint.type" value="floatingpoint.type.hard" valueType="enumerated"/>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.floatingpoint.enable.1271406314" name="Enable Hardware Floating Point (-mfpu=)" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.floatingpoint.enable" value="true" valueType="boolean"/>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.misc.dialect.896043443" name="C Language Dialect" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.misc.dialect" value="gnu.c.compiler.dialect.c18" valueType="enumerated"/>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.generateSecureCode.333068927" name="Generate Secure ARM Code (-mcmse)" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.generateSecureCode" value="true" valueType="boolean"/>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.optimization.level.665724753" name="Optimization Level" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.optimization.level" value="gnu.c.optimization.level.size" valueType="enumerated"/>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.optimization.datasect.2068516370" name="Place each data item into its own section (-fdata-sections)" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.optimization.datasect" value="true" valueType="boolean"/>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.optimization.functionsects.2018411406" name="Place each function into its own section (-ffunction-sections)" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.optimization.functionsects" value="true" valueType="boolean"/>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.optimization.omitframepointer.605122153" name="Omit frame pointer (-fomit-frame-pointer)" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.optimization.omitframepointer" value="true" valueType="boolean"/>
								<option id="gnu.c.compiler.option.warnings.allwarn.580699041" name="All warnings (-Wall)" superClass="gnu.c.compiler.option.warnings.allwarn" value="true" valueType="boolean"/>
								<option id="gnu.c.compiler.option.warnings.extrawarn.974304172" name="Extra warnings (-Wextra)" superClass="gnu.c.compiler.option.warnings.extrawarn" value="true" valueType="boolean"/>
								<option id="gnu.c.compiler.option.warnings.toerrors.399178781" name="Warnings as errors (-Werror)" superClass="gnu.c.compiler.option.warnings.toerrors" value="false" valueType="boolean"/>
								<option id="gnu.c.compiler.option.warnings.pedantic.2132799854" name="Pedantic (-pedantic)" superClass="gnu.c.compiler.option.warnings.pedantic" value="false" valueType="boolean"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.silabs.gnu.c.compiler.option.misc.otherlist.1517111467" name="Other flags" superClass="com.silabs.gnu.c.compiler.option.misc.otherlist" valueType="stringList">
									<listOptionValue builtIn="false" value="-fno-lto"/>
									<listOptionValue builtIn="false" value="--specs=nano.specs"/>
									<listOptionValue builtIn="false" value="-c"/>
									<listOptionValue builtIn="false" value="-fmessage-length=0"/>
								</option>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="gnu.c.compiler.option.include.paths.595580640" name="Include paths (-I)" superClass="gnu.c.compiler.option.include.paths" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/config}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/config/btconf}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/autogen}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/app/common/util/app_assert}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/app/common/util/app_timer}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/app/common/util/app_timer/bm}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/common/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/bgcommon/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/bgstack/ll/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/bootloader}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/bootloader/api}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/bootloader/core/flash}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/service/clock_manager/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/service/clock_manager/src}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/CMSIS/Core/Include}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/service/device_manager/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/emdrv/common/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/emlib/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/app/bluetooth/common/gatt_service_device_information_override}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/driver/gpio/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/peripheral/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/app/bluetooth/common/in_place_ota_dfu}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/service/interrupt_manager/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/service/interrupt_manager/src}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/service/interrupt_manager/inc/arm}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/config}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/config/preset}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/util/third_party/mbedtls/include}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/util/third_party/mbedtls/library}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/service/memory_manager/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/service/memory_manager/src}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/service/memory_manager/profiler/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/service/mpu/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/emdrv/nvm3/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/emdrv/nvm3/config}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/service/power_manager/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/radio/rail_lib/common}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/radio/rail_lib/protocol/ble}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/radio/rail_lib/protocol/ieee802154}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/radio/rail_lib/protocol/wmbus}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/radio/rail_lib/protocol/zwave}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/radio/rail_lib/chip/efr32/efr32xg2x}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/radio/rail_lib/protocol/sidewalk}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/pa-conversions}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/pa-conversions/efr32xg21}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/rail_util_power_manager_init}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/rail_util_pti}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/rail_util_rf_path}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/service/sl_main/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/service/sl_main/src}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/service/sleeptimer/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/security/sl_component/sli_crypto/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/security/sl_component/sl_protocol_crypto/src}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/security/sl_component/sli_psec_osal/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/service/udelay/inc}&quot;"/>
								</option>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.def.symbols.1393144798" name="Defined symbols (-D)" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.def.symbols" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="EFR32BG21A020F768IM32=1"/>
									<listOptionValue builtIn="false" value="SL_CODE_COMPONENT_SYSTEM=system"/>
									<listOptionValue builtIn="false" value="SL_APP_PROPERTIES=1"/>
									<listOptionValue builtIn="false" value="BOOTLOADER_APPLOADER=1"/>
									<listOptionValue builtIn="false" value="SL_CODE_COMPONENT_CLOCK_MANAGER=clock_manager"/>
									<listOptionValue builtIn="false" value="SL_COMPONENT_CATALOG_PRESENT=1"/>
									<listOptionValue builtIn="false" value="SL_CODE_COMPONENT_DEVICE_PERIPHERAL=device_peripheral"/>
									<listOptionValue builtIn="false" value="SL_CODE_COMPONENT_GPIO=gpio"/>
									<listOptionValue builtIn="false" value="SL_CODE_COMPONENT_HAL_COMMON=hal_common"/>
									<listOptionValue builtIn="false" value="SL_CODE_COMPONENT_HAL_GPIO=hal_gpio"/>
									<listOptionValue builtIn="false" value="SL_CODE_COMPONENT_INTERRUPT_MANAGER=interrupt_manager"/>
									<listOptionValue builtIn="false" value="CMSIS_NVIC_VIRTUAL=1"/>
									<listOptionValue builtIn="false" value="CMSIS_NVIC_VIRTUAL_HEADER_FILE=&quot;cmsis_nvic_virtual.h&quot;"/>
									<listOptionValue builtIn="false" value="MBEDTLS_CONFIG_FILE=&lt;sl_mbedtls_config.h&gt;"/>
									<listOptionValue builtIn="false" value="SL_CODE_COMPONENT_POWER_MANAGER=power_manager"/>
									<listOptionValue builtIn="false" value="MBEDTLS_PSA_CRYPTO_CONFIG_FILE=&lt;psa_crypto_config.h&gt;"/>
									<listOptionValue builtIn="false" value="SL_RAIL_LIB_MULTIPROTOCOL_SUPPORT=0"/>
									<listOptionValue builtIn="false" value="SL_RAIL_UTIL_PA_CONFIG_HEADER=&lt;sl_rail_util_pa_config.h&gt;"/>
									<listOptionValue builtIn="false" value="SL_CODE_COMPONENT_SE_MANAGER=se_manager"/>
									<listOptionValue builtIn="false" value="SL_CODE_COMPONENT_CORE=core"/>
									<listOptionValue builtIn="false" value="SL_RAIL_3_API=1"/>
									<listOptionValue builtIn="false" value="SL_CODE_COMPONENT_SLEEPTIMER=sleeptimer"/>
									<listOptionValue builtIn="false" value="SL_CODE_COMPONENT_SLI_CRYPTO=sli_crypto"/>
									<listOptionValue builtIn="false" value="SLI_RADIOAES_REQUIRES_MASKING=1"/>
									<listOptionValue builtIn="false" value="SL_CODE_COMPONENT_SLI_PROTOCOL_CRYPTO=sli_protocol_crypto"/>
									<listOptionValue builtIn="false" value="SL_CODE_COMPONENT_PSEC_OSAL=psec_osal"/>
								</option>
								<inputType id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.input.**********" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.input"/>
							</tool>
							<tool id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base.264952295" name="GNU ARM C++ Compiler" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.base">
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.floatingpoint.type.1545309946" name="Floating-Point ABI" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.floatingpoint.type" value="floatingpoint.type.hard" valueType="enumerated"/>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.floatingpoint.enable.414032151" name="Enable Hardware Floating Point (-mfpu=)" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.floatingpoint.enable" value="true" valueType="boolean"/>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.misc.dialect.133438456" name="C++ Language Dialect" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.misc.dialect" value="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.misc.dialect.cpp17" valueType="enumerated"/>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.no_rtti.776542572" name="No RTTI (-fno-rtti)" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.no_rtti" value="true" valueType="boolean"/>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.no_exceptions.208006584" name="No Exceptions (-fno-exceptions)" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.no_exceptions" value="true" valueType="boolean"/>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.generateSecureCode.1672151480" name="Generate Secure ARM Code (-mcmse)" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.generateSecureCode" value="true" valueType="boolean"/>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.optimization.level.1050599655" name="Optimization Level" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.optimization.level" value="gnu.cpp.compiler.optimization.level.size" valueType="enumerated"/>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.optimization.datasect.169268723" name="Place each data item into its own section (-fdata-sections)" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.optimization.datasect" value="true" valueType="boolean"/>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.optimization.functionsects.326310916" name="Place each function into its own section (-ffunction-sections)" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.optimization.functionsects" value="true" valueType="boolean"/>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.optimization.omitframepointer.711837139" name="Omit frame pointer (-fomit-frame-pointer)" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.compiler.optimization.omitframepointer" value="true" valueType="boolean"/>
								<option id="gnu.cpp.compiler.option.warnings.allwarn.1580974071" name="All warnings (-Wall)" superClass="gnu.cpp.compiler.option.warnings.allwarn" value="true" valueType="boolean"/>
								<option id="gnu.cpp.compiler.option.warnings.extrawarn.1718093196" name="Extra warnings (-Wextra)" superClass="gnu.cpp.compiler.option.warnings.extrawarn" value="true" valueType="boolean"/>
								<option id="gnu.cpp.compiler.option.warnings.toerrors.1160785596" name="Warnings as errors (-Werror)" superClass="gnu.cpp.compiler.option.warnings.toerrors" value="false" valueType="boolean"/>
								<option id="gnu.cpp.compiler.option.warnings.pedantic.411407624" name="Pedantic (-pedantic)" superClass="gnu.cpp.compiler.option.warnings.pedantic" value="false" valueType="boolean"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.silabs.gnu.cpp.compiler.option.misc.otherlist.1077179835" name="Other flags" superClass="com.silabs.gnu.cpp.compiler.option.misc.otherlist" valueType="stringList">
									<listOptionValue builtIn="false" value="-fno-lto"/>
									<listOptionValue builtIn="false" value="--specs=nano.specs"/>
									<listOptionValue builtIn="false" value="-c"/>
									<listOptionValue builtIn="false" value="-fmessage-length=0"/>
								</option>
							</tool>
							<tool id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.assembler.base.945393663" name="GNU ARM Assembler" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.assembler.base">
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.assembler.floatingpoint.type.1296669072" name="Floating-Point ABI" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.assembler.floatingpoint.type" value="floatingpoint.type.hard" valueType="enumerated"/>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.assembler.floatingpoint.enable.179563790" name="Enable Hardware Floating Point (-mfpu=)" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.assembler.floatingpoint.enable" value="true" valueType="boolean"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.assembler.includes.80747534" name="Include paths (-I)" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.assembler.includes" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/config}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/config/btconf}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/autogen}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/app/common/util/app_assert}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/app/common/util/app_timer}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/app/common/util/app_timer/bm}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/common/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/bgcommon/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/bgstack/ll/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/bootloader}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/bootloader/api}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/bootloader/core/flash}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/service/clock_manager/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/service/clock_manager/src}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/CMSIS/Core/Include}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/service/device_manager/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/emdrv/common/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/emlib/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/app/bluetooth/common/gatt_service_device_information_override}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/driver/gpio/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/peripheral/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/app/bluetooth/common/in_place_ota_dfu}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/service/interrupt_manager/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/service/interrupt_manager/src}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/service/interrupt_manager/inc/arm}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/config}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/config/preset}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/util/third_party/mbedtls/include}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/util/third_party/mbedtls/library}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/service/memory_manager/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/service/memory_manager/src}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/service/memory_manager/profiler/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/service/mpu/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/emdrv/nvm3/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/emdrv/nvm3/config}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/service/power_manager/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/radio/rail_lib/common}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/radio/rail_lib/protocol/ble}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/radio/rail_lib/protocol/ieee802154}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/radio/rail_lib/protocol/wmbus}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/radio/rail_lib/protocol/zwave}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/radio/rail_lib/chip/efr32/efr32xg2x}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/radio/rail_lib/protocol/sidewalk}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/pa-conversions}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/pa-conversions/efr32xg21}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/rail_util_power_manager_init}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/rail_util_pti}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/rail_util_rf_path}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/service/sl_main/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/service/sl_main/src}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/service/sleeptimer/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/security/sl_component/sli_crypto/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/security/sl_component/sl_protocol_crypto/src}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/security/sl_component/sli_psec_osal/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/service/udelay/inc}&quot;"/>
								</option>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.as.def.symbols.1475228691" name="Defined symbols (-D)" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.as.def.symbols" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="EFR32BG21A020F768IM32=1"/>
									<listOptionValue builtIn="false" value="SL_CODE_COMPONENT_SYSTEM=system"/>
									<listOptionValue builtIn="false" value="SL_APP_PROPERTIES=1"/>
									<listOptionValue builtIn="false" value="BOOTLOADER_APPLOADER=1"/>
									<listOptionValue builtIn="false" value="SL_CODE_COMPONENT_CLOCK_MANAGER=clock_manager"/>
									<listOptionValue builtIn="false" value="SL_COMPONENT_CATALOG_PRESENT=1"/>
									<listOptionValue builtIn="false" value="SL_CODE_COMPONENT_DEVICE_PERIPHERAL=device_peripheral"/>
									<listOptionValue builtIn="false" value="SL_CODE_COMPONENT_GPIO=gpio"/>
									<listOptionValue builtIn="false" value="SL_CODE_COMPONENT_HAL_COMMON=hal_common"/>
									<listOptionValue builtIn="false" value="SL_CODE_COMPONENT_HAL_GPIO=hal_gpio"/>
									<listOptionValue builtIn="false" value="SL_CODE_COMPONENT_INTERRUPT_MANAGER=interrupt_manager"/>
									<listOptionValue builtIn="false" value="CMSIS_NVIC_VIRTUAL=1"/>
									<listOptionValue builtIn="false" value="CMSIS_NVIC_VIRTUAL_HEADER_FILE=&quot;cmsis_nvic_virtual.h&quot;"/>
									<listOptionValue builtIn="false" value="MBEDTLS_CONFIG_FILE=&lt;sl_mbedtls_config.h&gt;"/>
									<listOptionValue builtIn="false" value="SL_CODE_COMPONENT_POWER_MANAGER=power_manager"/>
									<listOptionValue builtIn="false" value="MBEDTLS_PSA_CRYPTO_CONFIG_FILE=&lt;psa_crypto_config.h&gt;"/>
									<listOptionValue builtIn="false" value="SL_RAIL_LIB_MULTIPROTOCOL_SUPPORT=0"/>
									<listOptionValue builtIn="false" value="SL_RAIL_UTIL_PA_CONFIG_HEADER=&lt;sl_rail_util_pa_config.h&gt;"/>
									<listOptionValue builtIn="false" value="SL_CODE_COMPONENT_SE_MANAGER=se_manager"/>
									<listOptionValue builtIn="false" value="SL_CODE_COMPONENT_CORE=core"/>
									<listOptionValue builtIn="false" value="SL_RAIL_3_API=1"/>
									<listOptionValue builtIn="false" value="SL_CODE_COMPONENT_SLEEPTIMER=sleeptimer"/>
									<listOptionValue builtIn="false" value="SL_CODE_COMPONENT_SLI_CRYPTO=sli_crypto"/>
									<listOptionValue builtIn="false" value="SLI_RADIOAES_REQUIRES_MASKING=1"/>
									<listOptionValue builtIn="false" value="SL_CODE_COMPONENT_SLI_PROTOCOL_CRYPTO=sli_protocol_crypto"/>
									<listOptionValue builtIn="false" value="SL_CODE_COMPONENT_PSEC_OSAL=psec_osal"/>
								</option>
								<inputType id="org.eclipse.cdt.core.asmSource.1527859716" superClass="org.eclipse.cdt.core.asmSource"/>
							</tool>
							<tool id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.base.443440762" name="GNU ARM C Linker" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.base">
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.dependencies.projects.90754514" name="Project Dependencies" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.dependencies.projects"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="gnu.c.link.option.userobjs.653803510" name="Other objects" superClass="gnu.c.link.option.userobjs" valueType="userObjs">
									<listOptionValue builtIn="false" value="${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/bgcommon/lib/build/gcc/cortex-m33/bgcommon/release/libbgcommon.a}"/>
									<listOptionValue builtIn="false" value="${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/bgstack/ll/build/gcc/xg21/release/liblinklayer.a}"/>
									<listOptionValue builtIn="false" value="${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/bgstack/release/libbondingdb.a}"/>
									<listOptionValue builtIn="false" value="${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/ble_bgapi/release/libble_bgapi_gatt_client.a}"/>
									<listOptionValue builtIn="false" value="${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/ble_bgapi/release/libble_bgapi_gatt_server.a}"/>
									<listOptionValue builtIn="false" value="${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/bgapi_protocol/api3/release/libbgapi_core.a}"/>
									<listOptionValue builtIn="false" value="${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/accept_list/release/libble_host_accept_list_stub.a}"/>
									<listOptionValue builtIn="false" value="${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/bgstack/release/libble_host.a}"/>
									<listOptionValue builtIn="false" value="${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/ble_bgapi/release/libble_bgapi.a}"/>
									<listOptionValue builtIn="false" value="${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/ble_system/release/libble_system.a}"/>
									<listOptionValue builtIn="false" value="${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/connection_subrating/release/libble_host_connection_subrating_stub.a}"/>
									<listOptionValue builtIn="false" value="${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/core/release/libble_host_core.a}"/>
									<listOptionValue builtIn="false" value="${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/hal/release/libble_host_hal_series2.a}"/>
									<listOptionValue builtIn="false" value="${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/hci/release/libble_host_hci.a}"/>
									<listOptionValue builtIn="false" value="${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/system/release/libble_host_system.a}"/>
									<listOptionValue builtIn="false" value="${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/radio/rail_lib/autogen/librail_release/librail_efr32xg21_gcc_release.a}"/>
								</option>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.floatingpoint.type.702475656" name="Floating-Point ABI" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.floatingpoint.type" value="floatingpoint.type.hard" valueType="enumerated"/>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.floatingpoint.enable.713284409" name="Enable Hardware Floating Point (-mfpu=)" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.floatingpoint.enable" value="true" valueType="boolean"/>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.clibs.596012506" name="C Library" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.clibs" value="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.nanospec" valueType="enumerated"/>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.map.855945062" name="Generate map file (-Xlinker -Map=)" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.map" value="true" valueType="boolean"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.silabs.gnu.c.link.option.ldflags.441652575" name="Linker flags" superClass="com.silabs.gnu.c.link.option.ldflags" valueType="stringList">
									<listOptionValue builtIn="false" value="-Wl,--wrap=_free_r -Wl,--wrap=_malloc_r -Wl,--wrap=_calloc_r -Wl,--wrap=_realloc_r"/>
									<listOptionValue builtIn="false" value="-fno-lto"/>
									<listOptionValue builtIn="false" value="-Wl,--no-warn-rwx-segments"/>
								</option>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.circulardependency.393729605" name="Use library file circular dependency" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.circulardependency" value="true" valueType="boolean"/>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.nostdlibs.1178500008" name="No startup or default libs (-nostdlib)" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.nostdlibs" value="false" valueType="boolean"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="gnu.c.link.option.libs.615270781" name="Libraries (-l)" superClass="gnu.c.link.option.libs" valueType="libs">
									<listOptionValue builtIn="false" srcPrefixMapping="" srcRootPath="" value="gcc"/>
									<listOptionValue builtIn="false" srcPrefixMapping="" srcRootPath="" value="c"/>
									<listOptionValue builtIn="false" srcPrefixMapping="" srcRootPath="" value="m"/>
									<listOptionValue builtIn="false" srcPrefixMapping="" srcRootPath="" value="nosys"/>
								</option>
								<inputType id="cdt.managedbuild.tool.gnu.c.linker.input.1191181382" superClass="cdt.managedbuild.tool.gnu.c.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.base.1222578376" name="GNU ARM C++ Linker" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.base">
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.dependencies.projects.1464333338" name="Project Dependencies" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.dependencies.projects"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="gnu.cpp.link.option.userobjs.494900756" name="Other objects" superClass="gnu.cpp.link.option.userobjs" valueType="userObjs">
									<listOptionValue builtIn="false" value="${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/bgcommon/lib/build/gcc/cortex-m33/bgcommon/release/libbgcommon.a}"/>
									<listOptionValue builtIn="false" value="${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/bgstack/ll/build/gcc/xg21/release/liblinklayer.a}"/>
									<listOptionValue builtIn="false" value="${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/bgstack/release/libbondingdb.a}"/>
									<listOptionValue builtIn="false" value="${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/ble_bgapi/release/libble_bgapi_gatt_client.a}"/>
									<listOptionValue builtIn="false" value="${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/ble_bgapi/release/libble_bgapi_gatt_server.a}"/>
									<listOptionValue builtIn="false" value="${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/bgapi_protocol/api3/release/libbgapi_core.a}"/>
									<listOptionValue builtIn="false" value="${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/accept_list/release/libble_host_accept_list_stub.a}"/>
									<listOptionValue builtIn="false" value="${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/bgstack/release/libble_host.a}"/>
									<listOptionValue builtIn="false" value="${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/ble_bgapi/release/libble_bgapi.a}"/>
									<listOptionValue builtIn="false" value="${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/ble_system/release/libble_system.a}"/>
									<listOptionValue builtIn="false" value="${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/connection_subrating/release/libble_host_connection_subrating_stub.a}"/>
									<listOptionValue builtIn="false" value="${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/core/release/libble_host_core.a}"/>
									<listOptionValue builtIn="false" value="${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/hal/release/libble_host_hal_series2.a}"/>
									<listOptionValue builtIn="false" value="${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/hci/release/libble_host_hci.a}"/>
									<listOptionValue builtIn="false" value="${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/system/release/libble_host_system.a}"/>
									<listOptionValue builtIn="false" value="${workspace_loc:/${ProjName}/simplicity_sdk_2025.6.1/platform/radio/rail_lib/autogen/librail_release/librail_efr32xg21_gcc_release.a}"/>
								</option>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.floatingpoint.type.1118350294" name="Floating-Point ABI" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.floatingpoint.type" value="floatingpoint.type.hard" valueType="enumerated"/>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.floatingpoint.enable.734897564" name="Enable Hardware Floating Point (-mfpu=)" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.floatingpoint.enable" value="true" valueType="boolean"/>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.clibs.550388795" name="C Library" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.clibs" value="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.linker.nanospec" valueType="enumerated"/>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.map.856042701" name="Generate map file (-Xlinker -Map=)" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.map" value="true" valueType="boolean"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.silabs.gnu.cpp.link.option.flags.334011850" name="Linker flags" superClass="com.silabs.gnu.cpp.link.option.flags" valueType="stringList">
									<listOptionValue builtIn="false" value="-Wl,--wrap=_free_r -Wl,--wrap=_malloc_r -Wl,--wrap=_calloc_r -Wl,--wrap=_realloc_r"/>
									<listOptionValue builtIn="false" value="-fno-lto"/>
									<listOptionValue builtIn="false" value="-Wl,--no-warn-rwx-segments"/>
								</option>
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.circulardependency.624206560" name="Use library file circular dependency" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.cpp.linker.circulardependency" value="true" valueType="boolean"/>
							</tool>
							<tool id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.archiver.base.1687168659" name="GNU ARM Archiver" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.archiver.base">
								<option id="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.archiver.dependencies.projects.1772589421" name="Project Dependencies" superClass="com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.archiver.dependencies.projects"/>
							</tool>
						</toolChain>
					</folderInfo>
					<sourceEntries>
						<entry excluding="image/readme_img0.png|image/readme_img1.png|image/readme_img2.png|image/readme_img3.png|image/readme_img4.png|simplicity_sdk_2025.6.1/protocol/bluetooth/api/sl_bt.xapi|trashed_modified_files|bt_soc_empty_6_cmake|bt_soc_empty_6_iar_cmake" flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name=""/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
	</storageModule>
	<storageModule moduleId="com.silabs.ss.framework.ide.project.core.cpp" project.generation="190" projectCommon.boardIds="com.silabs.board.none:0.0.0" projectCommon.buildArtifactType="EXE" projectCommon.copiedFiles="[{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/src/sli_se_opaque_driver_aead.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1256806599},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/block_cipher_internal.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-2007901750},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_letimer.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1402760956},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/CMSIS/Core/Include/cmsis_version.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1961060320},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_aes.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1345911177},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_bluetooth_advertiser_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:13685381},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/device_manager/inc/sl_device_i2c.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1938734564},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sli_bt_sync_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-45550418},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/inc/aes_alt.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1092892438},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/mps_error.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1921319670},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/ctr.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1165816423},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/bootloader/api/btl_interface_parser.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:156093429},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/ssl_ticket.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1631762284},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/constant_time_internal.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1991435635},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sl_bt_api_compatibility.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1225742001},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;autogen/.slc_state/.crc_config.crc&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1449310564},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/camellia.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1811885161},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/src/sli_se_driver_key_derivation.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:364710820},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_semailbox.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-532745855},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/src/sli_se_manager_mailbox.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:846597150},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/app_assert_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:679635507},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/crypto_builtin_key_derivation.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1075420613},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/protocol/ble/rail_ble.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1701914822},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;autogen/sl_bluetooth.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1689853347},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emlib/src/em_msc.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:593165537},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/common/inc/sl_status.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1815280056},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;autogen/sl_bluetooth.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1945392242},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sli_bt_accept_list_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-781854065},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/device_manager/inc/sl_device_peripheral.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1738004621},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/src/sli_se_transparent_driver_hash.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-2075106676},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emdrv/nvm3/src/nvm3_object.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1187302255},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/device_manager/src/sl_device_peripheral.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-62578338},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/common/inc/sl_code_classification.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1171978255},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/mps_common.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1538774442},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_burtc.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-597193949},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/ssl_ciphersuites.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:100360135},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emlib/inc/em_msc_compat.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-891071826},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_emu.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:580626249},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/inc/se_management.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:372860636},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_storage.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-828283487},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/hkdf.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-2007927446},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/protocol/ble/sl_rail_ble.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1642470899},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/debug_internal.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1228027621},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/common/inc/sl_compiler.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-65106697},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/hal/release/libble_host_hal_series2.a&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:63497904},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/src/sli_se_opaque_driver_cipher.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1286089494},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/cmac.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1500920951},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/ecp_invasive.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:873593618},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/pk_internal.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1920757277},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/peripheral/src/sl_hal_system.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1499057675},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/ctr_drbg.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-201364967},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/sleeptimer/inc/sl_sleeptimer.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1263690971},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;autogen/gatt_db.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-131255043},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/pkwrite.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-395866397},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;autogen/gatt_db.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:529913917},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/clock_manager/src/sl_clock_manager_init.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-399450931},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_memory_manager_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1620681756},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_gpio.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1546366234},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/crypto_driver_contexts_primitives.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1224565945},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/memory_manager/profiler/src/sli_memory_profiler_stubs.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:561131891},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emdrv/nvm3/config/nvm3_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:566788975},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/system_efr32bg21.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1393006418},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/sl_main/inc/sl_main_init.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:190458763},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/inc/psa/internal_trusted_storage.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-22356735},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emdrv/nvm3/inc/nvm3_page.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:531266646},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_se.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:55132332},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_se.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:263094849},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/asn1.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1494932284},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/src/sli_psa_driver_common.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1930935454},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;autogen/linkerfile.ld&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-538109637},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/des.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1193552811},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_mac.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:794783864},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_mac.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-182538291},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emlib/inc/em_msc.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-2000424201},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/md_wrap.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1204786103},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/inc/psa/error.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-2038652123},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/src/sli_se_transparent_driver_aead.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1620019361},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_aead.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1168329796},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/memory_manager/src/sli_memory_manager.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-932903457},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/sleeptimer/src/sli_sleeptimer_hal.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:5545561},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/bootloader/core/btl_util.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:2110411372},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/platform_time.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1001466494},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_mbedtls_device_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1613143730},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/private_access.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-77783364},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/inc/sli_se_transparent_types.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-679286202},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/platform.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-624640287},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/src/sli_se_driver_mac.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1747739641},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/src/sli_psa_crypto.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:462138601},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_clock_manager_tree_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1942706772},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_aead.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:375577697},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sli_bgapi.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1898790400},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;autogen/sl_component_catalog.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1001234802},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/bgstack/release/libble_host.a&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:429837710},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/protocol/zwave/sl_rail_zwave.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-480853137},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_usart.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1394565855},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/ble_bgapi/release/libble_bgapi_gatt_server.a&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1963440884},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/src/sli_se_transparent_driver_cipher.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1147937499},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/crypto_struct.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1966747267},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/btl_interface_cfg.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:500184967},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emlib/src/em_rtcc.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1708799437},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/peripheral/inc/sl_hal_syscfg.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1103439493},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sli_bt_connection_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1318045227},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/src/sl_se_manager_hash.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1274638196},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emdrv/nvm3/inc/nvm3_hal_flash.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-2122198801},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/inc/sl_se_manager_key_handling.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1726731409},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_hash.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1093697643},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sli_bt_gattdb_def.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-179185044},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/inc/sli_psa_crypto.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1711476240},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/inc/sli_psa_driver_features.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-904421562},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/x509_csr.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1747835087},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/autogen/librail_release/librail_efr32xg21_gcc_release.a&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-841218370},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/protocol/wmbus/rail_wmbus.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:2129084879},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/connection_subrating/release/libble_host_connection_subrating_stub.a&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-818547821},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_hash.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:923059966},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sli_crypto/inc/sli_crypto.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-893219058},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sl_bt_api.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:519255352},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/common/sl_rail.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:419255803},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;image/readme_img0.png&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-674136934},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/asn1write.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:893132035},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/crypto_types.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1330577149},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/inc/sl_psa_values.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:613218289},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/mbedtls_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1234646717},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_devinfo.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1085359547},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/chachapoly.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1990778423},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/bgstack/release/libbondingdb.a&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:738626661},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emlib/inc/em_version.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:2082865380},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emdrv/nvm3/src/nvm3_hal_flash.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-238320602},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/base64_internal.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1316576365},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/crypto_adjust_config_dependencies.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1368871774},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/bgstack/ll/inc/sl_btctrl_linklayer_defs.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1175341797},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/clock_manager/inc/sl_clock_manager_init.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1297545247},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_i2c.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:557551005},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21a020f768im32.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1113081183},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sl_apploader_util.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-114322592},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/rsa_internal.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-400491584},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/bn_mul.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1117558497},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sl_bt_types.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:311677041},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sli_bt_pawr_advertiser_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1640472784},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/inc/sl_se_manager.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:689481245},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_driver_wrappers_no_static.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1507206673},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/udelay/src/sl_udelay.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:48187334},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/inc/sli_se_version_dependencies.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:194964729},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/CMSIS/Core/Include/core_cm33.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1576947287},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/peripheral/inc/sl_hal_system_generic.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:555820845},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_dma_descriptor.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1607183394},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/crypto_driver_contexts_key_derivation.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1964779934},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_driver_wrappers_no_static.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-399138545},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/cipher.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:14179716},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/src/sl_psa_its_nvm3.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1817729620},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/clock_manager/src/sl_clock_manager_init_hal_s2.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1109198501},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/chip/efr32/efr32xg2x/rail_chip_specific.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:361690105},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/ssl_debug_helpers.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-504154786},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/config_adjust_psa_from_legacy.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-181854210},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/interrupt_manager/src/sl_interrupt_manager_cortexm.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-812099575},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/inc/sl_se_manager_cipher.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-294132014},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/cipher.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1097930149},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/crypto_legacy.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-161695801},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/common/src/sl_core_cortexm.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-171014572},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/x509_crl.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-219441238},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/common/rail_features.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:260486820},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/inc/sha1_alt.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1801840173},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emlib/inc/em_bus.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1305471864},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/power_manager/src/sleep_loop/sl_power_manager_hal_s2.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1469926535},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/memory_manager/src/sl_memory_manager_region.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-420185503},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/pkcs12.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:148488795},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/src/sli_se_transparent_driver_mac.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-431051984},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/rsa_alt_helpers.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:699078306},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/mpu/inc/sl_mpu.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1867587885},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/aria.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-61270670},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/inc/sha256_alt.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:274461180},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;autogen/sli_psa_config_autogen.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1368404676},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/inc/sl_se_manager_attestation.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1554850848},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/common/inc/sl_assert.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1916068175},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/crypto_extra.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-2002864322},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emlib/inc/em_cmu.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1723423082},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/clock_manager/src/sl_clock_manager_hal_s2.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-311762303},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/power_manager/src/sleep_loop/sli_power_manager_private.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1075370649},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/app/common/util/app_timer/bm/app_timer_types.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-802801322},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/inc/sl_se_manager_entropy.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:319364281},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_dpll.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1240037977},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/src/sli_bt_external_bondingdb_config.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1692502480},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/ssl_cache.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-26825449},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/crypto_driver_contexts_composites.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1649745271},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/ssl_misc.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1416697571},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/crypto_adjust_config_key_pair_types.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1273677859},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/src/sli_se_version_dependencies.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1562635797},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Source/system_efr32bg21.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1526788737},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/pkcs5.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1009564819},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sl_bt_stack_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-772405766},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/chip/efr32/efr32xg2x/sl_rail_chip_specific.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1943364914},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/memory_manager/src/sl_memory_manager_pool.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1938422216},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_lvgd.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1392811578},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/poly1305.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1373628013},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emlib/inc/em_cmu_compat.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1608825958},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sli_bt_api.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-321507450},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/pk_wrap.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:175537441},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/protocol/zwave/rail_zwave.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-861085606},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/inc/ecjpake_alt.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:218536478},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/accept_list/release/libble_host_accept_list_stub.a&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1886502401},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emlib/src/em_cmu.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:572283866},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/padlock.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1951013488},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/crypto.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-562100320},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/memory_manager/src/sl_memory_manager_dynamic_reservation.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:551455836},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/src/sl_se_manager_signature.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-5929096},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/driver/gpio/inc/sl_gpio.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1315862098},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/peripheral/src/sl_hal_gpio.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:914536690},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/memory_manager/profiler/inc/sli_memory_profiler.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1302780653},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_btctrl_scheduler_priority_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1736234725},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/x509.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1807964817},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/common/rail_assert_error_codes.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:670103919},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/ssl_cookie.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:728373933},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/ssl_tls13_keys.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:438596114},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sli_bt_periodic_advertiser_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1259568609},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/md5.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1027403933},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/hmac_drbg.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1940885151},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_bluetooth_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1272351717},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/config_adjust_x509.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1544721110},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/ssl_client.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1785403308},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/error.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1690514448},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/ecdh.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:712244780},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/md_psa.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1119316747},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/inc/sli_se_transparent_functions.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-617547412},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/inc/sli_se_driver_mac.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-433272960},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/inc/sli_se_driver_cipher.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1150470593},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/pk.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1974131684},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emdrv/nvm3/inc/nvm3.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-828010452},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/mps_trace.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1968151968},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/sha512.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:805954865},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/check_crypto_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1206518326},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/hci/release/libble_host_hci.a&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:2126746418},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/CMSIS/Core/Include/tz_context.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1659747362},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/memory_manager/inc/sli_memory_manager_retention_control.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1757301961},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_btctrl_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1529403726},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_slot_management.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1184044970},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_wdog.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-105019087},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_storage.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-936110622},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_util_internal.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1236886222},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/threading.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1854493992},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;autogen/RTE_Components.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:794591649},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/common.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1374995934},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/protocol/sidewalk/sl_rail_sidewalk.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-294050525},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sli_bgapi_rtos_adaptation.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1202664574},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/app/common/util/app_assert/app_assert.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1148204550},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emlib/inc/em_timer.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:214988339},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_slot_management.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-323996695},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/common/rail_types.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1782626069},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emlib/inc/em_chip.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-2020711655},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/src/sli_bt_host_adaptation.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-448764368},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/src/sli_psa_driver_init.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1953035196},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/version.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-2052841494},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emdrv/common/inc/ecode.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1051405623},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/bgstack/ll/build/gcc/xg21/release/liblinklayer.a&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1305708401},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/app/common/util/app_timer/sl_simple_timer.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1410336744},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_gpio_port.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:2029434278},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/common/rail.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1380171333},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/power_manager/inc/sl_power_manager.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1228625886},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/inc/sl_se_manager_types.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:634180838},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emlib/inc/em_syscfg.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1187428177},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_lfrco.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1369115201},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/bignum_mod_raw_invasive.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:518060501},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_util.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:214322568},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/bgcommon/lib/build/gcc/cortex-m33/bgcommon/release/libbgcommon.a&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-655113573},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/src/sl_se_manager_attestation.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1754504484},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/config/sli_mbedtls_omnipresent.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1980329207},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/dhm.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-2145981030},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emlib/inc/em_system_generic.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1067340746},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emlib/inc/em_burtc.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1659655855},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/pkcs7.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:765745747},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/inc/sli_se_driver_aead.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1389358711},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/api/sl_bt.xapi&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1062683050},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/peripheral/inc/sl_hal_gpio.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1080519017},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emdrv/nvm3/src/nvm3_lock.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1769384725},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/base64.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1180579702},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/inc/sli_se_manager_internal.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-2075653655},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/sleeptimer/src/sl_sleeptimer_hal_prortc.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1244197903},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/sl_main/src/sl_main_init.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1701947879},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/entropy.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1872333506},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sli_bt_advertiser_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-394931551},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/common/src/sl_slist.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-836400225},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/block_cipher.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1180405176},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/src/sli_bt_pawr_advertiser_config.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:389543061},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_sleeptimer_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1439654632},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/src/sl_se_manager_key_handling.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-781761312},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/device_manager/inc/sl_device_gpio.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1133136101},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_rail_util_rf_path_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1970318505},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_core_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1630536524},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/build_info.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1055667389},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/md.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:769855353},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emdrv/nvm3/inc/nvm3_cache.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-947333248},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/pa-conversions/sl_rail_util_pa_curve_types_efr32.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1627490418},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/crypto_driver_common.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1328326102},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/crypto_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1712029369},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emlib/src/em_system.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:202612606},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/bgstack/ll/inc/sl_btctrl_linklayer.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:736673445},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/app/common/util/app_timer/bm/app_timer.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:664857332},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/driver/gpio/src/sl_gpio.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:923660157},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/power_manager/src/sleep_loop/sl_power_manager_debug.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1295369436},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/bgstack/ll/inc/sl_bt_ll_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:937920265},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/ble_bgapi/release/libble_bgapi_gatt_client.a&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1984931565},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/power_manager/src/common/sl_power_manager_common.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-945805515},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emlib/src/em_emu.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1535358092},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/cipher_wrap.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:131045280},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/sl_main/src/sl_main_process_action.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-2064357354},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sli_crypto/src/sl_crypto_s2.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:903854974},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/udelay/inc/sl_udelay.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-543325132},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/nist_kw.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1375379434},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emlib/inc/em_prs.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1532661055},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/cipher_wrap.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1088547327},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_random_impl.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1641436980},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/bignum_mod.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1331537950},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sli_crypto/inc/sli_crypto_s2.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-95657820},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_rail_util_pti_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:726551237},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/bootloader/api/btl_interface.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1714892585},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/common/src/sl_syscalls.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1086204842},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/common/inc/sli_code_classification.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1575591615},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/src/sli_se_opaque_key_derivation.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1793066290},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emlib/inc/em_rtcc.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1719715703},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emlib/src/em_gpio.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1356640438},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/bootloader/api/btl_interface.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1131712335},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_hfxo.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-4418928},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/sleeptimer/src/sl_sleeptimer_hal_burtc.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:550825456},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/btconf/in_place_ota_dfu.xml&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1550130987},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/src/sl_se_manager.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-604874996},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/common/src/sl_assert.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-583876453},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/ecjpake.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1627880937},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emlib/inc/em_core.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:977607732},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/bgcommon/inc/sli_bgcommon_debug.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1268003004},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sli_bt_dynamic_gattdb_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1852160794},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/config/sli_psa_acceleration.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-626959621},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/device_manager/src/sl_device_clock.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:911855222},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/bootloader/core/flash/btl_internal_flash.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:620010645},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/constant_time_impl.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:983875716},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/src/sli_se_opaque_driver_mac.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1442246187},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;bt_soc_empty_6.slpb&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1989347580},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/constant_time.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-2109203224},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/src/sl_apploader_util_s2.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-2041519730},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/bootloader/core/flash/btl_internal_flash.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1043014257},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_ldma.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-2110751242},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/memory_manager/inc/sl_memory_manager.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-848628349},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/inc/sha512_alt.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-2146416694},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/platform.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-935440897},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/pa-conversions/pa_curve_types_efr32.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1696368473},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/inc/sl_mbedtls.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:307570691},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/device_manager/inc/sl_device_clock.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:709640190},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/src/sli_se_driver_aead.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1100297020},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;app.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1862339718},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/power_manager/src/sleep_loop/sl_power_manager.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1898536704},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/device_manager/devices/sl_device_peripheral_hal_efr32xg21.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1334495230},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/crypto_builtin_primitives.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:51228072},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;sl_gatt_service_device_information_override.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1266614579},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/crypto_adjust_config_synonyms.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-22742909},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/threading.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:371473281},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/src/sl_mbedtls.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:571601859},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/power_manager/inc/sli_power_manager.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1986062660},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_syscfg.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-175702796},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;app.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1926989294},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_prs_signals.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-340847590},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/inc/psa/sli_internal_trusted_storage.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1134631923},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emlib/inc/em_emu.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1518750067},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;autogen/sli_mbedtls_config_transform_autogen.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1895119818},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_ffdh.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1873141298},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_ldmaxbar_defines.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1054567710},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/src/sli_se_driver_builtin_keys.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:991909460},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_ecp.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1738818736},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/inc/sl_se_manager_check_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1069133816},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/sha1.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-2045191477},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/bgstack/ll/inc/sl_btctrl_packets.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:387966423},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/src/sl_se_manager_util.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1030550021},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/app/common/util/app_assert/sl_app_assert.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1049380099},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/config_adjust_legacy_from_psa.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-2100903263},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/app/common/util/app_timer/app_timer.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1263662446},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_ecp.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1982818269},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/inc/sli_se_opaque_functions.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1970326654},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/clock_manager/inc/sl_clock_manager.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-759100733},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_bgapi_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1454988299},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;image/readme_img3.png&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-635345584},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_driver_wrappers.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:922516641},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/net_sockets.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1270265567},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/debug.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1137474377},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/power_manager/src/common/sl_power_manager_em4.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:364993770},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/clock_manager/inc/sli_clock_manager.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:854368945},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_mbedtls_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1083103663},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/crypto_adjust_auto_enabled.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:134468601},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_ffdh.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1571323550},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_protocol_crypto/src/sli_protocol_crypto_radioaes.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1270568276},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/bgapi_protocol/api3/release/libbgapi_core.a&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1130393969},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/common/inc/sl_common.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-343390933},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/sha256.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1420820710},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_core.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1059234169},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/constant_time.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:812184599},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/common/rail_mfm.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1949918614},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/common/inc/sl_core.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-66806949},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;image/readme_img4.png&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:651644810},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/sl_main/src/sli_main_init_memory.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:5649385},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/src/sli_psa_trng.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:762883864},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emdrv/nvm3/inc/nvm3_utils.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1401359504},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/config_psa.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1452457640},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/inc/sl_se_manager_hash.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-30157891},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/pa-conversions/efr32xg21/sl_rail_util_pa_curves.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1468955263},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/pa-conversions/sl_rail_util_pa_conversions_efr32.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1554038767},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_msc.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1367754507},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/crypto_platform.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1682063605},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_protocol_crypto/src/sli_protocol_crypto.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1107007940},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_cipher.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1163632939},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/memory_manager/src/sl_memory_manager_retarget.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1358031717},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_its.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-265612104},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_memory_manager_region_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:499972855},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/bignum_core.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1560721380},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emdrv/nvm3/inc/nvm3_object.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1990425636},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/bootloader/api/btl_interface_storage.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1117661336},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/inc/sli_se_driver_key_management.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-355722532},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_cipher.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:48352424},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/bootloader/api/btl_errorcode.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:2146265340},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/inc/sl_se_manager_key_derivation.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1940537576},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/bootloader/api/btl_interface_storage.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-33259306},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_bt_in_place_ota_dfu_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-768829936},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_bluetooth_connection_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-710373996},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/ssl_tls13_invasive.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-526563344},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sli_bt_external_bondingdb_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1844458538},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/CMSIS/Core/Include/cmsis_compiler.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1017116116},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/bgstack/ll/src/sl_btctrl_init.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1591530266},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_fsrco.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-603868516},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/crypto_values.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1493315424},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/psa_util.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1064017066},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/pa-conversions/pa_curves_efr32.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1887467524},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/src/sli_se_transparent_key_derivation.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1966218792},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/interrupt_manager/src/sli_interrupt_manager.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-481171395},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_cmu.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1642747625},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/app/bluetooth/common/gatt_service_device_information_override/sl_gatt_service_device_information_override.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:785469441},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/bignum.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1356991533},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/rail_util_pti/sl_rail_util_pti.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:601045995},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/inc/sli_se_manager_features.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:165234642},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/timing.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-104903100},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/rail_util_pti/sl_rail_util_pti.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1023890460},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/em_device.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1372544637},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_hfrco.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1857535609},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/ecdsa.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1872666039},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/common/inc/sl_atomic.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1121716419},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/pa-conversions/pa_conversions_efr32.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-2034408639},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_lfxo.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:806148702},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/ecp_internal_alt.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:241900409},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/bootloader/api/btl_reset_info.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1726785095},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/protocol/ieee802154/rail_ieee802154.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:786381159},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/common/inc/sl_bit.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1509628629},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/src/sl_psa_crypto.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-202664510},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/src/sl_bt_stack_init.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1499398136},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/nvm3_default_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1637484117},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/platform_util.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:739885619},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/src/sli_bt_connection_config.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:350834258},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/ripemd160.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-973489396},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;config/btconf/gatt_configuration.btconf&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1443038958},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/aes.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:831070353},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/config/sli_mbedtls_acceleration.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-11238163},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emlib/src/em_prs.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1968533221},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/protocol/wmbus/sl_rail_wmbus.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1048124463},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/pa-conversions/pa_conversions_efr32.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:442834878},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emdrv/nvm3/src/nvm3.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:939459656},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/rail_util_rf_path/sl_rail_util_rf_path.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:670927655},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/inc/sl_se_manager_defines.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1898000360},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/inc/ccm_alt.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1363022037},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/gcm.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1391784640},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;autogen/sl_application_type.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:884270272},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;main.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1374274860},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sl_bt_version.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-762807792},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/build_info.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1660860012},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_icache.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:396631948},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/rail_util_rf_path/sl_rail_util_rf_path.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1470233947},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/common/sl_rail_mfm.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1739990740},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emlib/src/em_burtc.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:584876533},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/crypto_sizes.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1198882484},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/sleeptimer/src/sl_sleeptimer_hal_rtcc.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:297035735},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/src/sli_se_driver_key_management.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1631366475},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/rsa.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-158314558},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/common/inc/sl_slist.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1208188628},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emdrv/nvm3/src/nvm3_cache.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-907209087},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/inc/sli_se_driver_key_derivation.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-907311705},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/lms.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1053099564},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/inc/sl_se_manager_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1438881046},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/src/sl_se_manager_entropy.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1770293398},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;autogen/sli_mbedtls_config_autogen.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1608622706},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emdrv/nvm3/inc/nvm3_lock.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:463938080},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emlib/inc/em_ramfunc.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-440195194},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_buram.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-8792064},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/src/sl_se_manager_key_derivation.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-319395213},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/sl_main/inc/sl_main_init_memory.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1652553233},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/rail_util_power_manager_init/sl_rail_util_power_manager_init.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1133355727},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/rail_util_power_manager_init/sl_rail_util_power_manager_init.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1356890490},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emlib/inc/em_system.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-703227574},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/src/sl_se_manager_cipher.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1772215682},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/bignum_internal.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:407889436},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/common/sl_rail_assert_error_codes.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1880533979},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/sleeptimer/src/sl_sleeptimer_hal_timer.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1529902264},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/common/inc/sl_enum.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:767800606},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/psa_crypto_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1170633160},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/compat-2.x.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1294979913},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_gpcrc.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1421700738},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/entropy_poll.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1258873761},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_clock_manager_oscillator_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:619763042},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/ecp.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:990349747},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/x509_internal.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:199163574},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emdrv/nvm3/src/nvm3_utils.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-685825997},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/crypto_se_driver.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1469126699},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emlib/inc/em_core_generic.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1510564519},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/config_adjust_legacy_crypto.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1334684948},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;autogen/sli_psa_builtin_config_autogen.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1328919745},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sli_bt_host_adaptation.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1509448132},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/src/sli_bt_sync_config.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-27899395},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/sleeptimer/inc/sli_sleeptimer.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-675396244},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/interrupt_manager/inc/arm/cmsis_nvic_virtual.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1195757326},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/x509_crt.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-2065798957},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_pake.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1906477277},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/ble_system/release/libble_system.a&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-910831129},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sl_bt_stack_init.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-2069025428},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_pake.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-2105821673},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/bignum_mod_raw.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:54733502},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/power_manager/inc/sl_power_manager_debug.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1950774890},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_timer.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:692223031},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/ccm.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1509009618},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/inc/cmac_alt.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-217189748},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/src/sli_bt_advertiser_config.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1082210133},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sli_psec_osal/inc/sli_psec_osal.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1453130352},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sli_bt_config_defs.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-781605445},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/src/sli_se_driver_signature.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-562647614},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/memory_manager/src/sl_memory_manager.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-820536542},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_rail_util_pa_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:744197080},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;app_bm.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-527404556},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/peripheral/inc/sl_hal_system.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-2085575040},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emlib/src/em_timer.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-513234271},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/memory_buffer_alloc.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-802804035},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/src/sli_bt_dynamic_gattdb_config.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-2112605655},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/plugin/pa-conversions/pa_curves_efr32.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-353936850},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emdrv/nvm3/inc/nvm3_trace.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:458379096},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/chacha20.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1898511724},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_core_common.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1948890714},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/src/sli_bt_periodic_advertiser_config.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:492667813},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/app_properties_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1656311952},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/inc/sli_se_manager_mailbox.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1728076644},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sli_psec_osal/inc/sli_psec_osal_baremetal.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-434223498},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/sleeptimer/src/sl_sleeptimer.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1932521168},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/clock_manager/src/sli_clock_manager_init_hal.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:863244302},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/bootloader/app_properties/app_properties.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1871829857},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/src/sli_bt_accept_list_config.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-577204564},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/memory_manager/src/sli_memory_manager_common.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1749468827},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/device_manager/inc/sl_device_peripheral_types.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-92473937},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emdrv/nvm3/inc/nvm3_generic.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1718035403},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/bootloader/api/application_properties.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1711065910},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;image/readme_img1.png&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-361491007},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;autogen/sl_power_manager_handler.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:614712728},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_bufc.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1160901362},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/lmots.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-498320348},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/common/sl_rail_features.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1163144530},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/sl_main/inc/sl_main_process_action.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1414299874},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;autogen/sl_event_handler.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:274366627},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_power_manager_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-985497135},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/memory_manager/inc/sl_memory_manager_region.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1567703618},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_acmp.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1157438500},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/crypto_builtin_composites.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1960829859},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_client.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:2061929318},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;autogen/sl_event_handler.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1871223443},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/peripheral/inc/sl_hal_bus.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:734418046},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/src/sli_bt_l2cap_config.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-154693048},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/common/sl_rail_types.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1410194524},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emdrv/nvm3/src/nvm3_page.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1959576077},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_rsa.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:33254066},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_rsa.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:659330128},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emdrv/nvm3/src/nvm3_default_common_linker.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1987708252},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_prs.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:704757671},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;autogen/gen.properties&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1847033395},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;readme.md&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:223618935},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emdrv/nvm3/inc/nvm3_default.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-542251721},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/inc/sl_se_manager_internal_keys.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:711949574},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_ulfrco.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1657793635},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/clock_manager/src/sli_clock_manager_hal.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-359124826},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/memory_manager/src/sl_memory_manager_pool_common.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1003861226},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sli_bt_l2cap_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:587031476},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/psa/crypto_compat.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1505707924},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/bgcommon/src/sli_bgcommon_debug_efr32.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1462989274},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/inc/sli_se_opaque_types.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1384435149},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/inc/sl_se_manager_signature.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-257511430},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto_invasive.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1763524041},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_smu.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:522905649},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_rail_util_power_manager_init_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1634398877},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/device_manager/src/sl_device_gpio.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:2134675053},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/mps_reader.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-334190533},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emlib/inc/em_common.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1528133626},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/bgstack/ll/src/sl_btctrl_init_tasklets.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1490455152},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/oid.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1987211566},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/pem.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-2137818038},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/clock_manager/src/sl_clock_manager.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1171918086},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_iadc.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-303666566},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/psa_crypto.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1084191472},{&quot;generated&quot;:true,&quot;projectPath&quot;:&quot;config/sl_bt_host_adaptation_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:71330515},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sl_bgapi.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1735177648},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/CMSIS/Core/Include/cmsis_gcc.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:965562395},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/protocol/sidewalk/rail_sidewalk.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1325061698},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/radio/rail_lib/protocol/ieee802154/sl_rail_ieee802154.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-2019498837},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/bgstack/ll/inc/sl_btctrl_callbacks.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-539287999},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/src/sli_se_driver_cipher.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-392223819},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/inc/sl_psa_crypto.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1508806497},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/alignment.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-2076996152},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/inc/sl_bt_ll_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:937920265},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/app/common/util/app_timer/bm/app_timer_internal.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:6320264},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_rtcc.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-787656034},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emlib/inc/em_gpio.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1097767613},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/app/bluetooth/common/in_place_ota_dfu/sl_bt_in_place_ota_dfu.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-856425733},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emdrv/nvm3/inc/nvm3_hal.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-686570572},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/check_config.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-34450299},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_psa_driver/inc/sli_psa_driver_common.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-48722375},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/app/bluetooth/common/in_place_ota_dfu/sl_bt_in_place_ota_dfu.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:284635903},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/interrupt_manager/inc/sl_interrupt_manager.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-2054094161},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/system/release/libble_host_system.a&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-84360531},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Include/efr32bg21_ldmaxbar.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:222757388},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/udelay/src/sl_udelay_armv6m_gcc.S&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:295142276},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/Device/SiliconLabs/EFR32BG21/Source/startup_efr32bg21.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1640049093},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/ssl.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-831550863},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_protocol_crypto/src/sli_radioaes_management.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1015990419},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/se_manager/inc/sl_se_manager_util.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-566231474},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_protocol_crypto/src/sli_radioaes_management.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1540642916},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/core/release/libble_host_core.a&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-480422435},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/sha3.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-236568924},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/platform_util.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-2081422803},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/library/ssl_ciphersuites_internal.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1984991304},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emlib/inc/em_assert.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-768224831},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;image/readme_img2.png&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:568097369},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/config_adjust_ssl.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1141948284},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/sl_main/src/sl_main_init_memory.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1363539023},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/protocol/bluetooth/build/gcc/cortex-m33/ble_host/ble_bgapi/release/libble_bgapi.a&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:430774186},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/CMSIS/Core/Include/mpu_armv8.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1593587326},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/emlib/inc/sli_em_cmu.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-129567129},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/util/third_party/mbedtls/include/mbedtls/config_adjust_psa_superset_legacy.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1907218314},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/service/mpu/src/sl_mpu_s2.c&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:-1743925665},{&quot;generated&quot;:false,&quot;projectPath&quot;:&quot;simplicity_sdk_2025.6.1/platform/security/sl_component/sl_mbedtls_support/inc/gcm_alt.h&quot;,&quot;originalPath&quot;:&quot;&quot;,&quot;version&quot;:1,&quot;content&quot;:1180063988}]" projectCommon.ideId="simplicity-ide" projectCommon.importModeId="COPY" projectCommon.partId="mcu.arm.efr32.bg21.efr32bg21a020f768im32" projectCommon.savedStockVariables="{&quot;copiedSdkLocation&quot;:&quot;simplicity_sdk_2025.6.1&quot;,&quot;partOpn&quot;:&quot;efr32bg21a020f768im32&quot;}" projectCommon.sdkId="com.silabs.sdk.stack.sisdk:2025.6.1._309472562" projectCommon.toolchainId="com.silabs.ss.tool.ide.arm.toolchain.gnu.cdt:12.2.1.20221205"/>
	<storageModule moduleId="cdtBuildSystem" version="4.0.0">
		<project id="bt_soc_empty_6.com.silabs.ss.framework.ide.project.core.cdt.cdtMbsProjectType.812300462" name="SLS CDT Project" projectType="com.silabs.ss.framework.ide.project.core.cdt.cdtMbsProjectType"/>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.core.LanguageSettingsProviders"/>
	<storageModule moduleId="scannerConfiguration">
		<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		<scannerConfigBuildInfo instanceId="com.silabs.ss.framework.project.toolchain.core.default#com.silabs.ss.tool.ide.arm.toolchain.gnu.cdt:12.2.1.20221205;com.silabs.ss.framework.project.toolchain.core.default#com.silabs.ss.tool.ide.arm.toolchain.gnu.cdt:12.2.1.20221205.;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.base.807393319;com.silabs.ide.si32.gcc.cdt.managedbuild.tool.gnu.c.compiler.input.**********">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.make.core.buildtargets"/>
</cproject>