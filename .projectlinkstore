{"data": [{"hit": 5, "alias": "app.c", "type": "FILE", "data": "D:/2025BLE/0825/bt_soc_empty_6/app.c"}, {"hit": 1, "alias": "main.c", "type": "FILE", "data": "D:/2025BLE/0825/bt_soc_empty_6/main.c"}, {"hit": 1, "alias": "Software Components", "type": "NAVIGATION", "data": "softwarecomponents"}, {"hit": 0, "alias": "readme.md", "type": "FILE", "data": "D:/2025BLE/0825/bt_soc_empty_6/readme.md"}, {"hit": 0, "alias": "Post Build Editor", "type": "TOOL", "data": "postbuildeditor"}, {"hit": 0, "alias": "Bluetooth GATT Configurator", "type": "TOOL", "data": "bleGattConfigurator"}, {"hit": 0, "alias": "<PERSON><PERSON>", "type": "TOOL", "data": "pintool"}, {"hit": 0, "alias": "Memory Editor", "type": "TOOL", "data": "memoryeditor"}, {"hit": 0, "alias": "docs.silabs.com", "type": "EXTERNAL_URL", "data": "https://docs.silabs.com/"}]}